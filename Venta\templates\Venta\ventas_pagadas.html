{% extends 'BaseInicio/base.html' %}

{% block content %}
<br></br>
<div class="container" style="max-width: 90%;">
    <a href="{% url 'ListaPagoPendiente' %}" class="btn btn-warning">Regresar</a>
    <br></br> 
    <h2>Ventas al Crédito Pagadas</h2>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Venta</th>
                <th>NIT</th>
                <th>Nombre</th>
                <th>Dirección</th>
                <th>Monto Total</th>
                <th>Fecha de Pago</th>
                <th>Estado</th>
                <th>Acciones</th> <!-- Nueva columna para acciones -->
            </tr>
        </thead>
        <tbody>
            {% for venta in ventas_pagadas %}
            <tr>
                <td>{{ venta.factura }}</td>
                <td>{{ venta.nit }}</td>
                <td>{{ venta.nombre }}</td>
                <td>{{ venta.direccion }}</td>
                <td>{{ venta.total }}</td>
                <td>{{ venta.fecha|date:"Y-m-d" }}</td> <!-- Formato de fecha -->
                <td>
                    {% if venta.estado == 1 %}
                        Pagada
                    {% elif venta.estado == 3 %}
                        A Crédito
                    {% elif venta.estado == 4 %}
                        Cancelada
                    {% endif %}
                </td>
                <td>
                    <a href="{% url 'pdf_creditoVenta' venta.factura %}">Generar PDF</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7">No hay ventas pagadas registradas.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
