from io import BytesIO
from django.http import HttpResponse
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet
from .models import ProductoFactura, PagoFacturaCredito

class ReporteFacturaCredito:
    def __init__(self, factura_id):
        self.buf = BytesIO()
        self.factura = ProductoFactura.objects.get(factura=factura_id)

    def run(self):
        self.doc = SimpleDocTemplate(self.buf, pagesize=A4)
        self.story = []
        self.encabezado()
        self.crear_tabla_abonos()
        self.doc.build(self.story)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        styles = getSampleStyleSheet()
        imagen_logo = Image('Venta/logo.jpg', width=95, height=50, hAlign='RIGHT')
        
        self.story.append(Paragraph(f"Reporte de Pago - Factura #{self.factura.factura}", styles['Title']))
        self.story.append(Paragraph(f"Fecha: {self.factura.fecha_factura}", styles['Normal']))
        self.story.append(Paragraph(f"Total de la deuda: Q{self.factura.total}", styles['Normal']))
        self.story.append(Spacer(1, 0.2 * inch))

    def crear_tabla_abonos(self):
        abonos = PagoFacturaCredito.objects.filter(factura=self.factura)
        data = [["Fecha", "Monto"]] + [[abono.fecha, f"Q{abono.abono}"] for abono in abonos]
        
        total_abonado = sum(abono.abono for abono in abonos)
        saldo_pendiente = self.factura.total - total_abonado

        data.append(["Total Abonado", f"Q{total_abonado}"])
        data.append(["Saldo Pendiente", f"Q{saldo_pendiente}"])

        table = Table(data, colWidths=[4 * inch, 2 * inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black)
        ]))
        self.story.append(table)

