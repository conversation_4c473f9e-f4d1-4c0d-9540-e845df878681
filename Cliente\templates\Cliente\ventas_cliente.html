{% extends 'BaseInicio/base.html' %}
{% block title %}Ventas del Cliente{% endblock %}
{% block carta %}Ventas de {{ cliente.nombre }}{% endblock %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h3>Ventas de {{ cliente.nombre }}</h3>
    <p><strong>NIT:</strong> {{ cliente.nit }}</p>

    <!-- Filtro por rango de fechas -->
    <form method="GET" class="form-inline mb-4">
        <label for="fecha_inicio" class="mr-2">Desde:</label>
        <input type="text" id="fecha_inicio" name="fecha_inicio" class="form-control mr-3 flatpickr" placeholder="YYYY-MM-DD" value="{{ fecha_inicio }}">
        <label for="fecha_fin" class="mr-2">Hasta:</label>
        <input type="text" id="fecha_fin" name="fecha_fin" class="form-control mr-3 flatpickr" placeholder="YYYY-MM-DD" value="{{ fecha_fin }}">
        <button type="submit" class="btn btn-primary">Filtrar</button>
    </form>

    <!-- Listado de ventas -->
    <table class="table table-striped">
        <thead>
            <tr>
                <th># Factura</th>
                <th>Fecha</th>
                <th>Monto</th>
                <th>Estado</th>
                <th>Detalle</th>
            </tr>
        </thead>
        <tbody>
            {% for venta in ventas %}
            <tr>
                <td>{{ venta.factura }}</td>
                <td>{{ venta.serie }}</td>
                <td>{{ venta.fecha }}</td>
                <td>{{ venta.total }}</td>
                <td>{{ venta.cliente.nombre }}</td>
                <td>
                    {% if venta.detalle_set.exists %}
                        <button class="btn btn-info btn-sm" data-toggle="modal" 
                                data-target="#ventaModal" onclick="loadVentaDetail({{ venta.pk }})">
                            Ver
                        </button>
                    {% else %}
                        <button class="btn btn-danger btn-sm" onclick="confirmDelete({{ venta.pk }})">
                            Eliminar
                        </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Modal para ver los detalles de la venta -->
<div class="modal fade" id="ventaModal" tabindex="-1" aria-labelledby="ventaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ventaModalLabel">Detalle de Venta</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Aquí se cargará el contenido de la venta -->
            </div>
        </div>
    </div>
</div>

<!-- Flatpickr para fechas -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        flatpickr('.flatpickr', {
            enableTime: false,
            dateFormat: "Y-m-d",
        });
    });

    function loadVentaDetail(ventaId) {
        fetch(`/venta/detalle_venta_cliente/${ventaId}/`)
            .then(response => response.text())
            .then(html => {
                document.getElementById('modal-body').innerHTML = html;
                $('#ventaModal').modal('show');
            });
    }
    

    function confirmDelete(ventaId) {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Obtener el csrf_token desde el cookie
                const csrftoken = getCookie('csrftoken');
        
                fetch(`/venta/eliminar/${ventaId}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken  // Agregar el CSRF token en los headers
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.deleted) {
                        Swal.fire('Eliminado', 'La venta ha sido eliminada.', 'success');
                        location.reload();  // Recargar la página para reflejar la eliminación
                    } else {
                        Swal.fire('Error', 'No se pudo eliminar la venta.', 'error');
                    }
                });
            }
        });
    }
    
    // Función para obtener el valor del csrf_token de las cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}
