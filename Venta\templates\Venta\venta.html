{% extends 'BaseInicio/base.html' %}
{% block title %}Venta{% endblock %}
{% block carta %}<i class="fa-solid fa-house"></i> Venta{% endblock %}
{% load static %}
{% block content %}

{% if messages %}
    {% for message in messages %}

    {% if message.tags == "success" %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Finalizada!",
            "html": "<a href='{% url 'PDF' message %}'' class='btn btn-danger'>PDF</a>",
            "icon": "{{message.tags}}"
        })
    </script>
    {% elif message.tags == "info" %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Factura",
            "html": "<a href='{{message}}'' class='btn btn-danger' target='_blank'>FEL</a>",
            "icon": "{{message.tags}}"
        })
    </script>
    {% else %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "Venta Descartada!",
            "html": "{{message}}",
            "icon": "{{message.tags}}"
        })
    </script>
    {% endif %}

    {% endfor %}
    {% endif %}

<br>
  <div class="container">
    <a href="{% url 'ListaVentaHoy2' %}"><button class="btn btn-warning">Lista de Ventas del Dia</button></a>
    </div>
<br>
<div class="container container-venta">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">Datos Venta</h4>
        <p class="card-description">
          Fecha Venta <strong>{% now 'd-m-Y' %}</strong>
        </p>
        <form class="forms-sample" method="POST" enctype="multipart/form-data">{% csrf_token %}
          <div class="row">
            <div class="col-md-4">
                    <label for="">Venta</label>
                    <input type="text" name="factura" value="{{v}}" class="form-control" readonly>
                </div>
                <div class="col-md-4">
                  <label for="">Tipo de Venta</label>
                  <select name="tipo_venta" class="form-control">
                    <option value="Contado">FEL</option>
                    <option value="Proforma">Proforma</option>
                    <option value="Cotizacion">Cotizacion</option>
                  </select>     
            
              </div>
              <div class="col-md-4">
                    <label for="">Nit de Cliente</label>
                    <input type="text" name="nit" autofocus="nit" placeholder="Nit de Cliente" class="form-control" maxlength="15">  
                </div>
          </div><br>
          <div class="row">
            <div class="col-md-4">
                <label for="">Nombre del Cliente</label>
                <input type="text" name="nombre" class="form-control" placeholder="Nombre del Cliente" maxlength="250">  
            </div>
            <div class="col-md-4">
                <label for="">Direccion de Cliente</label>
                <input type="text" name="direccion" class="form-control" placeholder="Direccion del Cliente" maxlength="850">     
            </div>
          </div><br>
<!--          <div class="row">
            <div class="col-6">
              <label for="">Tipo de Cliente</label>
              <select name="tipo_cliente" class="form-control">
                <option value="Normal">Normal</option>
                <option value="Descuento">Descuento</option>
                <option value="Mayorista">Mayorista</option>
              </select>
               </div>     
          </div>-->
           
          <br>
          <button type="submit" class="btn btn-primary mr-2">Iniciar</button>
          <button class="btn btn-light">Cancel</button>
        </form>
      </div>
    </div>
</div>
{% endblock %}