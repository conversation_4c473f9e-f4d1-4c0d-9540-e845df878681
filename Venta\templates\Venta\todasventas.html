{% extends 'BaseInicio/base.html' %}
{% block title %}Lista de Todas Las Ventas{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i> Lista de Todas Las Ventas {% endblock %}
{% load static %}

{% block content %}
<br>
<div class="container">
    <a href="{% url 'Venta' %}"><button class="btn btn-info">Nueva Venta</button></a>
    <a href="{% url 'ListaVentaTodasCredito' %}"><button class="btn btn-warning">Ventas al Credito</button></a>
    <br><br>

    <div class="tile">
        <h3 class="tile-title">Lista de Todas Las Ventas</h3>

        <!-- Formulario para búsqueda por fecha y texto -->
        <form method="get" class="form-inline mb-3">
            <div class="form-group mr-2">
                <label for="fecha" class="mr-2">Buscar por fecha:</label>
                <input type="date" class="form-control" name="fecha" id="fecha" value="{{ request.GET.fecha }}">
            </div>
            <input type="text" class="form-control ml-2" name="search" placeholder="Buscar por nombre, factura, etc..." value="{{ request.GET.search }}">
            <button type="submit" class="btn btn-primary ml-2">Filtrar</button>
            <a href="{% url 'ListaVentaTodas' %}" class="btn btn-secondary ml-2">Limpiar</a>
        </form>

        {% if messages %}
            {% for message in messages %}
                <script>
                    Swal.fire({
                        "title": "Información del Sistema",
                        "text": "{{ message }}",
                        "icon": "{{ message.tags }}"
                    });
                </script>
            {% endfor %}
        {% endif %}

        <div class="table-responsive">
            <table class="table table-bordered order-table">
                <thead>
                    <tr>
                        <th>Venta #</th>
                        <th>Nombre de Cliente</th>
                        <th>Total</th>
                        <th>Estado</th>
                        <th>Tipo</th>
                        <th>Fecha</th>
                        <th>Usuario</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for p in todas %}
                    <tr>
                        <td>{{ p.factura }}</td>
                        <td>{{ p.nombre }}</td>
                        <td>Q.{{ p.total }}</td>
                        <td>
                            {% if p.estado == 1 %} Terminada
                            {% elif p.estado == 2 %} Anulada
                            {% elif p.estado == 3 %} No pagado
                            {% else %} Pendiente/Sin Terminar
                            {% endif %}
                        </td>
                        <td>
                            {% if p.tipo == "Contado" %}
                                <span class="badge badge-info">FEL</span>
                            {% elif p.tipo == "Proforma" %}
                                <span class="badge badge-success">Proforma</span>
                            {% elif p.tipo == "Credito" %}
                                <span class="badge badge-primary">Credito</span>
                            {% elif p.tipo == "CreditoFel" %}
                                <span class="badge badge-info">Credito FEL</span>
                            {% else %}
                                <span class="badge badge-warning">Cotización</span>
                            {% endif %}
                        </td>
                        <td>{{ p.fecha|date:"d-m-Y" }}</td>
                        <td>{{ p.usuario.username }}</td>
                        <td>
                            {% if p.estado == 2 %}
                                {% if p.token %}
                                    <a href="{% url 'PDF' p.token %}" target="_blank">
                                        <button class="btn btn-danger btn-sm">
                                            <i class="fa fa-file-pdf"></i> <b>-Anulada</b>
                                        </button>
                                    </a>
                                {% else %}
                                    <button class="btn btn-danger btn-sm" disabled>
                                        <i class="fa fa-file-pdf"></i> <b>-Anulada</b> (No disponible)
                                    </button>
                                {% endif %}
                            {% else %}
                                {% if p.tipo != "Contado" and p.tipo != "CreditoFel" %}
                                    {% if p.token %}
                                        <a href="{% url 'Detalle' p.token %}">
                                            <button class="btn btn-warning btn-sm">
                                                <i class="fa fa-pencil" title="Modificar"></i>
                                            </button>
                                        </a>
                                        <a href="{% url 'PasarFEL' p.token %}">
                                            <button class="btn btn-secondary btn-sm">
                                                <b>A FEL</b>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-warning btn-sm" disabled>Modificar</button>
                                    {% endif %}
                                {% endif %}
                                
                                {% if p.tipo == "Contado" %}
                                    {% if p.link %}
                                        <a href="{{ p.link }}" target="_blank">
                                            <button class="btn btn-info btn-sm">
                                                <i class="fa fa-file-pdf"></i> <b>FEL</b>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-info btn-sm" disabled>FEL no disponible</button>
                                    {% endif %}
                                    {% if p.token %}
                                        <a href="{% url 'AnulaFEL' p.token %}">
                                            <button class="btn btn-danger btn-sm">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-danger btn-sm" disabled>Anular FEL</button>
                                    {% endif %}
                                {% elif p.tipo == "CreditoFel" %}
                                    {% if p.link %}
                                        <a href="{{ p.link }}" target="_blank">
                                            <button class="btn btn-info btn-sm">
                                                <i class="fa fa-file-pdf"></i> <b>FEL</b>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-info btn-sm" disabled>FEL no disponible</button>
                                    {% endif %}
                                    {% if p.estado == 3 %}
                                        <a href="{% url 'pagar_credito_fel' p.token %}">
                                            <button class="btn btn-success btn-sm">
                                                <i class="fa fa-money"></i> Pagar
                                            </button>
                                        </a>
                                    {% endif %}
                                    <a href="{% url 'AnulaFEL' p.token %}">
                                        <button class="btn btn-danger btn-sm">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </a>
                                    <a href="{% url 'detalle_credito_fel' p.token %}">
                                        <button class="btn btn-primary btn-sm">
                                            <i class="fa fa-eye"></i> Ver
                                        </button>
                                    </a>
                                {% else %}
                                    {% if p.token %}
                                        <a href="{% url 'PDF' p.token %}">
                                            <button class="btn btn-success btn-sm">
                                                <i class="fa fa-file-pdf"></i>
                                            </button>
                                        </a>
                                        <a href="{% url 'Anular' p.token %}">
                                            <button class="btn btn-danger btn-sm">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-success btn-sm" disabled>PDF no disponible</button>
                                        <button class="btn btn-danger btn-sm" disabled>Anular</button>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr><td colspan="8" class="text-center">No hay ventas para mostrar.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Información de rendimiento -->
        <div class="text-center mt-3">
            <small class="text-muted">
                Mostrando {{ todas|length }} ventas
                {% if not search and not fecha %}
                    (limitado a las últimas 500 para mejor rendimiento)
                {% endif %}
            </small>
        </div>

    </div>
</div>

<!-- Filtro de tabla local simple -->
<script>
(function (document) {
    'use strict';
    var LightTableFilter = (function (Arr) {
        var _input;
        function _onInputEvent(e) {
            _input = e.target;
            var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
            Arr.forEach.call(tables, function (table) {
                Arr.forEach.call(table.tBodies, function (tbody) {
                    Arr.forEach.call(tbody.rows, _filter);
                });
            });
        }
        function _filter(row) {
            var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
            row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        }
        return {
            init: function () {
                var inputs = document.getElementsByClassName('light-table-filter');
                Arr.forEach.call(inputs, function (input) {
                    input.oninput = _onInputEvent;
                });
            }
        };
    })(Array.prototype);
    document.addEventListener('readystatechange', function () {
        if (document.readyState === 'complete') {
            LightTableFilter.init();
        }
    });
})(document);
</script>

{% endblock %}
