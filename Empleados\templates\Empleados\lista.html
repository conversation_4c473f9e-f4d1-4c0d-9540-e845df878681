{% extends 'BaseInicio/base.html' %}


{% block content %}


    <div class="col-md-12">
        {% if messages %}
        {% for message in messages %}
        {{message}}
        {% endfor %}
        {% endif %}
        <br></br>
        <div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>    
    <a href="{% url 'NuevoEmpleado' %}" class="btn btn-primary">Nuevo Empleado</a>
    <a href="{% url 'ListadoPlanilla' %}" class="btn btn-danger">Lista de Panilla</a>

    <div class="col-14">
        <div class="card my-4">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                    <h6 class="text-white text-capitalize ps-3">LISTADO DE EMPLEADOS REGISTRADOS</h6>
                </div>
            </div>
        <div class="card-body px-4 pb-2">
       
        <div class="table-responsive">

        <table class="table">
            <thead>
                <tr>
                    <th scope="col">DPI</th>
                    <th scope="col">Nombres</th>
                    <th scope="col">Apellidos</th>
                    <th scope="col">Puesto</th>
                    <th scope="col">Salario Base</th>
                    <th scope="col">Inicio Laboral</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                {% for e in emp %}
                <tr>
                    <th scope="row">{{e.dpi}}</th>
                    <td>{{e.nombre}}</td>
                    <td>{{e.apellido}}</td>
                    <td>{{e.puesto}}</td>
                    <td>Q.{{e.salario_base}}</td>
                    <td>{{e.fecha}}</td>
                    <td>
                       
                        <a href="{% url 'Modificar' e.dpi %}"
                        class="text-info font-weight-bold text-ms" data-toggle="tooltip"
                        data-original-title="Edit user">
                        Editar
                    </a><br>
                        <a href="{% url 'Eliminar' e.dpi  %}"
                        class="text-danger font-weight-bold text-ms" data-toggle="tooltip"
                        data-original-title="Edit user">
                        Eliminar
                    </a>
                    </td>
                </tr>
                {% empty %}
                <caption>SIN REGISTRO DE EMPLEADOS</caption>
                {% endfor %}
            </tbody>
        </table>
    </div>
    </div>
    </div>
    </div>

</div>

{% endblock %}