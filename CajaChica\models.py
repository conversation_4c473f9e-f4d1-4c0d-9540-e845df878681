from django.db import models
from django.utils import timezone

class CajaChica(models.Model):
    cantidad = models.DecimalField(max_digits=10, decimal_places=2)
    fecha_creacion = models.DateField(auto_now_add=True)

    def __str__(self):
        return f"Caja Chica - {self.fecha_creacion} - {self.cantidad}"
    
    class Meta:
        ordering = ['-fecha_creacion']
        unique_together = ['fecha_creacion']
