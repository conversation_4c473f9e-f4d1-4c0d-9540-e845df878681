from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Categoria.models import Categoria
from Categoria.forms import CategoriaForm,UpdateCategoriaForm
from user.models import User


@login_required
def nueva(request):
    form = CategoriaForm()
    if request.method == "POST":
        form = CategoriaForm(request.POST)
        if form.is_valid():
            c = Categoria()
            c.nombre = form.cleaned_data['nombre']
            c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
           # c.usuario = User.objects.get(id=request.user.id)
            c.save()
            messages.success(request,f'{c.nombre} Ingresado!')
            return redirect('ListaCategoria')
    
    return render(request,'Categoria/nueva.html',{'form':form})



@login_required
def listado(request):
    datos = Categoria.objects.all().order_by('id')
    return render(request,'Categoria/lista.html',{'cate':datos})


@login_required
def actualizar(request,id):
    cate = Categoria.objects.get(id=id)
    if request.method == 'GET':
        form = UpdateCategoriaForm(instance=cate)
    else:
        form = UpdateCategoriaForm(request.POST,instance = cate)
     
        if form.is_valid():
            try:
                cate.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                cate.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(request, f'Categoria {cate.nombre} Modificada Exitosamente!')
                return redirect('ListaCategoria')
            except:
                messages.error(request, f'No Se Pudo Modificar {cate.nombre}!')
                return redirect('ListaCategoria')

    return render(request,'Categoria/actualizar.html',{'form':form})
    
    

@login_required
def eliminar(request,id):
    try:
        cate = Categoria.objects.get(id=id)
        cate.delete()
        messages.success(request,f'{cate.nombre} Eliminado!')
        return redirect('ListaCategoria')
    except:
        messages.error(request,f'No Se Puede Eliminar {cate.nombre}')
        return redirect('ListaCategoria')        

