from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Pagos.models import PagoFacturaCredito
from Proveedor.models import Proveedor
from Producto.models import Producto, ProductoFactura, DetalleFactura, Bitacora
from Producto.forms import ProductoForm, UpdateProductoForm, ProductoFacturaForm
from user.models import User
#import xlwt
from django.http import HttpResponse
from django.db.models import Q
from django.db.models import Sum
from Categoria.models import Categoria


@login_required
def nuevo(request):
    p = Proveedor.objects.all()
    form = ProductoFacturaForm()
    if request.method == "POST":
        if request.user.rol == "admin":
                form = ProductoFacturaForm(request.POST)
                if form.is_valid():
                    p = ProductoFactura()
                    p.factura = form.cleaned_data['factura']
                    p.serie = form.cleaned_data['serie']
                    p.fecha_factura = form.cleaned_data['fecha_factura']
                    p.cantidad = form.cleaned_data['cantidad']
                    p.total = form.cleaned_data['total']
                    p.id_prov = Proveedor.objects.get(
                        nit=request.POST['id_prov'])
                    p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    p.usuario = User.objects.get(id=request.user.id)
                    p.estado = 1
                    p.save()
                    messages.success(
                        request, f'Factura {p.factura} Ingresada!')
                    return redirect('DetalleIngresoFactura', p.factura)
        else:
            form = ProductoFacturaForm(request.POST)
            if form.is_valid():
                p = ProductoFactura()
                p.factura = form.cleaned_data['factura']
                p.serie = form.cleaned_data['serie']
                p.fecha_factura = form.cleaned_data['fecha_factura']
                p.cantidad = form.cleaned_data['cantidad']
                p.total = form.cleaned_data['total']
                p.id_prov = Proveedor.objects.get(nit=request.POST['id_prov'])
                p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                p.usuario = User.objects.get(id=request.user.id)
                p.estado = 1
                p.save()
                messages.success(request, f'Factura {p.factura} Ingresada!')
                return redirect('DetalleIngresoFactura', p.factura)

    return render(request, 'Producto/nuevofactura.html', {'form': form, 'c': p})



@login_required
def detalle(request, f):
    h = False
    cate = Categoria.objects.all()

    if ProductoFactura.objects.filter(factura=f).exists():
        factura = ProductoFactura.objects.get(factura=f)
        detalles = DetalleFactura.objects.filter(factura=f)
        items = DetalleFactura.objects.filter(factura=f, estado=1).aggregate(it=Sum('cantidad'))
        tot = DetalleFactura.objects.filter(factura=f, estado=1).aggregate(t=Sum('total'))
    else:
        messages.error(request, '¡Está intentando ingresar productos sin una factura previa!')
        return redirect('IngresoFactura')

    if request.method == 'POST':
        if 'buscar' in request.POST:
            query = request.POST['buscar']
            tipo = request.POST['tipo']

            # Buscar por nombre o código
            if tipo == 'nombre':
                buscar = Producto.objects.filter(nombre__icontains=query)
            else:
                buscar = Producto.objects.filter(codigo=query)

            if buscar.exists():
                b = True
                h = False
            else:
                b = False
                h = True  # Mostrar formulario para agregar nuevo producto

            return render(request, 'Producto/detallefactura.html', {
                'f': f, 'c': factura, 'd': detalles, 'b': b, 'buscar': buscar,
                'i': items['it'], 'total': tot['t'], 'h': h, 'cate': cate,
                'query': query, 'tipo': tipo  # Pasar la búsqueda y tipo para usarlos en el formulario
            })

        elif 'crear_producto' in request.POST:
            nombre = request.POST.get('nuevo_nombre')
            codigo = request.POST.get('nuevo_codigo')

            if not nombre or not codigo:
                messages.error(request, '¡Debe proporcionar tanto el nombre como el código del producto!')
                return redirect('DetalleIngresoFactura', f)

            p = Producto(
                nombre=nombre,
                codigo=codigo,
                stock=0,
                precio_compra=Decimal(0.00),
                precio_venta=Decimal(0.00),
                id_cate=Categoria.objects.get(id=request.POST['cate']),
                fecha=datetime.today().strftime('%Y-%m-%d'),
                usuario=request.user
            )
            p.save()
            messages.success(request, f'¡Producto {p.codigo} - {p.nombre} creado e ingresado exitosamente!')
            return redirect('DetalleIngresoFactura', f)

        elif 'agregar' in request.POST:
            try:
                prod = Producto.objects.get(id=request.POST['id'])
                d = DetalleFactura()
                d.factura = ProductoFactura.objects.get(factura=f)
                d.id_prod = prod

                # Si no se ingresan precios nuevos, usa los actuales
                d.compra_antes = prod.precio_compra
                d.compra_ahora = Decimal(request.POST['nuevocompra']) if request.POST['nuevocompra'] else prod.precio_compra
                d.venta_antes = prod.precio_venta
                d.venta_ahora = Decimal(request.POST['nuevoventa']) if request.POST['nuevoventa'] else prod.precio_venta
                d.stock_antes = prod.stock
                d.stock_ahora = prod.stock + int(request.POST['cantidad'])
                d.cantidad = int(request.POST['cantidad'])
                d.total = d.cantidad * d.compra_ahora
                d.fecha = datetime.today()
                d.usuario = request.user
                d.estado = 1
                d.save()
                
                Producto.objects.filter(id=prod.id).update(
                    stock=d.stock_ahora,
                    ingreso=prod.ingreso + d.cantidad,
                    precio_compra=d.compra_ahora,
                    precio_venta=d.venta_ahora
                )
                
                messages.success(request, f'¡Se ingresaron {d.cantidad} unidades del producto {d.id_prod.nombre}!')
                return redirect('DetalleIngresoFactura', f)
            except Exception as e:
                messages.error(request, f'Error al ingresar el producto. ¡Revise los datos! Error: {e}')
                return redirect('DetalleIngresoFactura', f)

        elif 'quitar' in request.POST:
            try:
                datos = DetalleFactura.objects.get(id=request.POST['corr'], factura=f)
                prod = Producto.objects.get(id=datos.id_prod.id)
                
                Producto.objects.filter(id=prod.id).update(
                    stock=prod.stock - datos.cantidad,
                    ingreso=prod.ingreso - datos.cantidad,
                    precio_compra=datos.compra_antes,
                    precio_venta=datos.venta_antes
                )
                
                datos.delete()
                messages.success(request, f'¡Se quitaron {datos.cantidad} unidades del producto {datos.id_prod.nombre}! Inventario actualizado.')
                return redirect('DetalleIngresoFactura', f)
            except Exception as e:
                messages.error(request, f'¡No se pudo quitar {datos.cantidad} unidades del producto {datos.id_prod.nombre}! Inventario no actualizado. Error: {e}')
                return redirect('DetalleIngresoFactura', f)

        elif 'terminar' in request.POST:
            # Verificar si el checkbox está presente
            if 'credito' in request.POST:
                factura.estado = 3
            else:
                factura.estado = 1

            # Verificar si el valor del estado ha cambiado
            print(f"Estado actual: {factura.estado}")
            factura.total = factura.total
            factura.save()
            messages.success(request, 'Factura actualizada correctamente')
            return redirect('IngresoFactura')

    return render(request, 'Producto/detallefactura.html', {
        'f': f, 'c': factura, 'd': detalles, 'i': items['it'],
        'total': tot['t'], 'h': h, 'cate': cate
    })


@login_required
def lista(request):
    # Filtra las facturas que están en estado 1 (Pagada)
    facturas_pagadas = ProductoFactura.objects.filter(estado=1)

    # Filtra los pagos relacionados con las facturas pagadas
    pagos = PagoFacturaCredito.objects.filter(factura__in=facturas_pagadas)

    datos = {
        'facturas_pagadas': facturas_pagadas,
        'pagos': pagos
    }
    return render(request, 'Producto/facturas_pagadas.html', {'datos': datos})



@login_required
def darbaja(request, f):

    if ProductoFactura.objects.filter(factura=f).exists():
        for d in DetalleFactura.objects.filter(factura=f, estado=1):
            prod = Producto.objects.get(id=d.id_prod.id)
            Producto.objects.filter(id=prod.id).update(stock=prod.stock-d.cantidad, ingreso=prod.ingreso -
                                                       d.cantidad, precio_compra=d.compra_antes, precio_venta=d.venta_antes)
            DetalleFactura.objects.filter(
                factura=f, estado=1).update(estado=99)

        ProductoFactura.objects.filter(factura=f, estado=1).update(estado=99)
        messages.success(
            request, f'Productos de Factura # {f} Fueron Dados de Baja Inventario de Estos Productos Vuelve a Precios y Stock Anterior!')
        return redirect('ListaIngresoFactura')

    else:
        messages.error(
            request, f'Error Al Dar de Baja Factura de Ingreso # {f}!')
        return redirect('ListaIngresoFactura')



def bitacora(id, id_pr, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id_pr
    b.prod = prod
    b.tipo = t
    b.doc = id
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()   