from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from Producto.models import Producto
from Producto.forms import ProductoForm, UpdateProductoForm
from user.models import User
from django.http import HttpResponse
from Categoria.models import Categoria
from django.shortcuts import render, get_object_or_404, redirect
from decimal import Decimal
from .models import Producto, Categoria 
from django.db.models import Q



@login_required
def nuevo(request):
    form = ProductoForm()
    if request.method == "POST":
        form = ProductoForm(request.POST)
        if form.is_valid():
            codigo = form.cleaned_data['codigo']
            ingreso = form.cleaned_data['ingreso']
            
            # Verificar si el producto con ese código ya existe
            producto_existente = Producto.objects.filter(codigo=codigo).first()
            
            if producto_existente:
                # Actualizar el stock del producto existente
                producto_existente.stock += ingreso
                producto_existente.save()
                messages.success(request, f'Stock de {producto_existente.nombre} actualizado con éxito.')
            else:
                # Crear un nuevo producto
                p = Producto()
                p.codigo = codigo
                p.nombre = form.cleaned_data['nombre']
                p.descripcion = form.cleaned_data['descripcion']
                p.stock = ingreso  # Asignar el valor de ingreso como stock inicial
                p.ingreso = ingreso
                p.precio_compra = form.cleaned_data['precio_compra']
                p.precio_venta = Decimal(form.cleaned_data['precio_venta'])
                p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                p.usuario = User.objects.get(id=request.user.id)
                
                # Asignar la categoría
                p.id_cate = form.cleaned_data['id_cate']
                
                p.save()
                messages.success(request, f'Producto {p.nombre} ingresado con éxito.')

            return redirect('ListaProducto')

    return render(request, 'Producto/nuevo.html', {'form': form})



def actualizar(request, id):
    producto = get_object_or_404(Producto, id=id)

    if request.method == 'POST':
        form = UpdateProductoForm(request.POST, instance=producto)
        if form.is_valid():
            ingreso = form.cleaned_data.get('ingreso', 0)  # Obtener ingreso con un valor predeterminado de 0
            producto = form.save(commit=False)  # Guardar producto sin escribir en la base de datos
            producto.stock += ingreso  # Sumar ingreso al stock actual
            producto.save()  # Guardar los cambios en la base de datos
            return redirect('ListaProducto')  # Redirigir a la lista de productos
    else:
        # Aquí se establece el valor inicial de 'ingreso' a 0 cuando se carga el formulario
        form = UpdateProductoForm(instance=producto, initial={'ingreso': 0})

    return render(request, 'Producto/actualizar.html', {'form': form})

from django.http import JsonResponse

@login_required
def listado(request):
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 25))
    search = request.GET.get('search', '').strip()

    productos = Producto.objects.all()

    if search:
        productos = productos.filter(
            Q(nombre__icontains=search) |
            Q(codigo__icontains=search) |
            Q(id_cate__nombre__icontains=search)
        )

    productos = productos.order_by('-id')

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        productos = productos[offset:offset+limit]
        resultado = []
        for p in productos:
            resultado.append({
                'id': p.id,
                'codigo': p.codigo,
                'nombre': p.nombre,
                'categoria': str(p.id_cate),
                'stock': p.stock,
                'precio_compra': str(p.precio_compra),
                'precio_venta': str(p.precio_venta),
                'update_url': reverse('UpdateProducto', args=[p.id]),
                'delete_url': reverse('DeleteProducto', args=[p.id]),
                'bitacora_url': reverse('bitacora_producto', args=[p.id])
            })
        return JsonResponse({'productos': resultado})

    # Para la carga inicial, mostrar solo los primeros 25 registros
    productos_iniciales = productos[:25]
    return render(request, 'Producto/lista.html', {'prod': productos_iniciales})


def actualizar2(request, id):
    producto = get_object_or_404(Producto, id=id)

    if request.method == 'POST':
        form = UpdateProductoForm(request.POST, instance=producto)
        if form.is_valid():
            ingreso = form.cleaned_data.get('ingreso', 0)  # Obtener ingreso con un valor predeterminado de 0
            producto = form.save(commit=False)  # Guardar producto sin escribir en la base de datos
            producto.stock += ingreso  # Sumar ingreso al stock actual
            producto.save()  # Guardar los cambios en la base de datos
            return redirect('ListaProducto')  # Redirigir a la lista de productos
    else:
        # Aquí se establece el valor inicial de 'ingreso' a 0 cuando se carga el formulario
        form = UpdateProductoForm(instance=producto, initial={'ingreso': 0})

    return render(request, 'Producto/actualizar2.html', {'form': form})
    

@login_required
def listado2(request):
    datos = Producto.objects.all()
    return render(request, 'Producto/lista2.html', {'prod': datos})



@login_required
def eliminar(request, id):
    try:
        prod = Producto.objects.get(id=id)
        prod.delete()
        messages.success(request, f'{prod.nombre} Eliminado!')
        return redirect('ListaProducto')
    except:
        messages.error(request, f'No Se Puede Elimnar {prod.nombre}')
        return redirect('ListaProducto')



def bitacora(id, prod, t, d, h, i, s, hy, u):

    b = Bitacora()
    b.id_prod = 0
    b.prod = prod
    b.tipo = t
    b.doc = 0
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()           



from django.shortcuts import render, get_object_or_404
from Producto.models import Producto, Bitacora, DetalleFactura, ProductoFactura
from Venta.models import Detalle, Venta
from datetime import datetime
from django.db.models import Q
from Proveedor.models import Proveedor

ESTADO_CANCELADA = 2  # Ajusta según tu modelo

def bitacora_producto(request, producto_id):
    producto = get_object_or_404(Producto, id=producto_id)

    # Obtener fechas desde el formulario GET
    fecha_inicio = request.GET.get('fecha_inicio')
    fecha_fin = request.GET.get('fecha_fin')

    # Cotizaciones a excluir
    coti = Venta.objects.filter(
        estado=1,
        tipo__iexact='cotizacion'
    )

    # Entradas (compras)
    entradas = DetalleFactura.objects.filter(id_prod=producto_id)
    if fecha_inicio and fecha_fin:
        entradas = entradas.filter(fecha__range=[fecha_inicio, fecha_fin])
    entradas = entradas.select_related('usuario').order_by('fecha')

    # Salidas (ventas no cotización)
    salidas = Detalle.objects.filter(id_inventario=producto_id).exclude(factura__in=coti)
    if fecha_inicio and fecha_fin:
        salidas = salidas.filter(fecha__range=[fecha_inicio, fecha_fin])
    salidas = salidas.select_related('usuario').order_by('fecha')

    # Obtener proveedor más reciente si hay entradas
    proveedor = None
    if entradas.exists():
        try:
            ultima_entrada = entradas.last()
            factura_id = ultima_entrada.factura
            if not isinstance(factura_id, int):
                factura_id = factura_id.factura
            ultima_factura = ProductoFactura.objects.get(factura=factura_id)
            proveedor = ultima_factura.id_prov
        except ProductoFactura.DoesNotExist:
            proveedor = None

    movimientos = []
    facturas_cache = {}

    for entrada in entradas:
        factura_id = entrada.factura
        if not isinstance(factura_id, int):
            factura_id = factura_id.factura

        if factura_id in facturas_cache:
            factura_obj = facturas_cache[factura_id]
        else:
            try:
                factura_obj = ProductoFactura.objects.get(factura=factura_id)
            except ProductoFactura.DoesNotExist:
                factura_obj = None
            facturas_cache[factura_id] = factura_obj

        proveedor_nombre = factura_obj.id_prov.nombre if factura_obj and factura_obj.id_prov else 'No disponible'

        if factura_obj and hasattr(factura_obj, 'estado'):
            if factura_obj.estado == 1:
                condicion_pago = 'Contado'
            elif factura_obj.estado == 3:
                condicion_pago = 'Crédito'
            else:
                condicion_pago = 'Otro'
        else:
            condicion_pago = 'N/A'

        movimientos.append({
            'fecha': entrada.fecha,
            'tipo': 'ENTRADA',
            'documento': f"Factura {factura_id}" if factura_id else 'N/A',
            'cantidad': entrada.cantidad,
            'salida': 0,
            'precio_unitario': getattr(entrada, 'compra_ahora', 0.00),
            'total': getattr(entrada, 'total', 0.00),
            'usuario': entrada.usuario.username if entrada.usuario else 'Sistema',
            'origen': 'compra',
            'proveedor_nombre': proveedor_nombre,
            'anulada': False,
            'estado_pago': condicion_pago,
        })

    for salida in salidas:
        cantidad_salida = getattr(salida, 'salio', getattr(salida, 'cantidad', 0))
        anulada = salida.estado == ESTADO_CANCELADA
        movimientos.append({
            'fecha': salida.fecha,
            'tipo': 'SALIDA',
            'documento': f"Factura {salida.factura}" if salida.factura else 'N/A',
            'cantidad': 0,
            'salida': cantidad_salida,
            'precio_unitario': getattr(salida, 'precio_unitario', 0.00),
            'total': getattr(salida, 'total', 0.00),
            'usuario': salida.usuario.username if salida.usuario else 'Sistema',
            'origen': 'venta',
            'proveedor_nombre': '-',
            'anulada': anulada,
            'estado_pago': '',
        })

    # Ordenar movimientos cronológicamente
    movimientos_ordenados = sorted(movimientos, key=lambda x: x['fecha'])

    # Calcular stock hacia atrás
    stock_actual = producto.stock
    for mov in reversed(movimientos_ordenados):
        mov['stock_actual'] = stock_actual
        if mov['tipo'] == 'ENTRADA':
            stock_actual -= mov['cantidad']
        elif mov['tipo'] == 'SALIDA':
            stock_actual += mov['salida']
    movimientos_ordenados.reverse()

    # Totales
    total_comprado = sum(m['cantidad'] for m in movimientos_ordenados if m['tipo'] == 'ENTRADA')
    total_vendido = sum(
        m['salida'] for m in movimientos_ordenados 
        if m['tipo'] == 'SALIDA' and not m['anulada']
    )

    return render(request, 'Producto/movimiento_producto.html', {
        'producto': producto,
        'movimientos': movimientos_ordenados,
        'resumen': {
            'total_comprado': total_comprado,
            'total_vendido': total_vendido,
        },
        'proveedor': proveedor,
        'fecha_inicio': fecha_inicio,
        'fecha_fin': fecha_fin,
    })
