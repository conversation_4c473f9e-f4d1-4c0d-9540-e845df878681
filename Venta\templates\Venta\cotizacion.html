{% extends 'BaseInicio/base.html' %}
{% block title %}Lista de Todas Las Cotizaciones{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i>Lista de Todas Las Ventas{% endblock %}
{% load static %}
{% block content %}

<div class="col-md-12 container-table">
    <div class="tile">
        <br>
        <h3 class="tile-title">Lista de Todas Las Cotizaciones</h3><br>

        <!-- Filtro por fecha -->
        <form method="get" class="form-inline mb-3">
            <input type="date" name="fecha_inicio" class="form-control mr-2" value="{{ fecha_inicio|default:'' }}">
            <input type="date" name="fecha_fin" class="form-control mr-2" value="{{ fecha_fin|default:'' }}">
            <button type="submit" class="btn btn-primary">Filtrar por Fecha</button>
            <a href="{% url 'lista_cotizacion' %}" class="btn btn-secondary ml-2">Limpiar</a>
        </form>

        <!-- Mostrar mensaje si no hay resultados -->
        {% if not coti %}
            <div class="alert alert-warning" role="alert">
                No se encontraron cotizaciones en el rango de fechas seleccionado.
            </div>
        {% endif %}

        <div class="col-md-6">
            {% if messages %}
            {% for message in messages %}
            <script>
                Swal.fire({
                    "title": "Informacion Sistema",
                    "text": "Cotizacion Finalizada!",
                    "html": "<a href='{% url 'PDFCotizacion' message %}'' class='btn btn-danger'>PDF</a>",
                    "icon": "{{message.tags}}"
                })
            </script>
            {% endfor %}
            {% endif %}
        </div><br>

        <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar">
        
        <div class="table-responsive">
            <table class="table table-bordered order-table">
                <thead style="font-size: 14px;">
                    <tr>
                        <th>Cotizacion #</th>
                        <th>Nit de Cliente</th>
                        <th>Nombre de Cliente</th>
                        <th>Direccion de Cliente</th>
                        <th>Total</th>
                        <th>Estado</th>
                        <th>Fecha</th>
                        <th>Usuario</th>
                        <th>PDF</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody style="font-size: 14px;">
                    {% for p in coti %}
                    <tr>
                        <td>{{ p.factura }}</td>
                        <td>{{ p.nit }}</td>
                        <td>{{ p.nombre }}</td>
                        <td>{{ p.direccion }}</td>
                        <td>Q.{{ p.total }}</td>
                        <td>
                            {% if p.estado == 1 %}
                                Terminada
                            {% elif p.estado == 2 %}
                                Anulada
                            {% elif p.estado == 3 %}
                                Cotizacion
                            {% else %}
                                Pendiente/Sin Terminar
                            {% endif %}
                        </td>
                        <td>{{ p.fecha|date:"d-m-Y" }}</td>
                        <td>{{ p.usuario.username }}</td>
                        <td>
                            <a href="{% url 'pdfcotizacion' p.token %}"><button class="btn btn-success btn-sm"><i class="fa-solid fa-file-pdf"></i></button></a>
                        </td>
                        <td>
                            <a href="{% url 'PDFCOTIZACION' p.token %}"><button class="btn btn-warning btn-sm"><i class="fa-solid fa-edit"></i></button></a>
                            <a href="{% url 'AnularCotizacion' p.token %}"><button class="btn btn-danger btn-sm"><i class="fa-solid fa-trash" title="Anular Cotizaci��n"></i></button></a>     
                        </td>
                    </tr>
                    {% empty %}
                    <caption>SIN COTIZACIONES</caption>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    (function (document) {
        'use strict';

        var LightTableFilter = (function (Arr) {

            var _input;

            function _onInputEvent(e) {
                _input = e.target;
                var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                Arr.forEach.call(tables, function (table) {
                    Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                    });
                });
            }

            function _filter(row) {
                var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
            }

            return {
                init: function () {
                    var inputs = document.getElementsByClassName('light-table-filter');
                    Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                    });
                }
            };
        })(Array.prototype);

        document.addEventListener('readystatechange', function () {
            if (document.readyState === 'complete') {
                LightTableFilter.init();
            }
        });

    })(document);
</script>

{% endblock %}
