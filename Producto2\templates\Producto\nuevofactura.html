{% extends 'BaseInicio/base.html' %}
{% block title %}Nuevo Ingreso Factura{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}


<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 70%;"><br>



    {{form.errors}}

    <div class="container-xxl flex-grow-1 container-p-y">

        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario Ingreso Factura</h5>
                        <small class="text-muted float-end">Factura</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Factura</label>
                                    {{form.factura}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Serie</label>
                                    {{form.serie}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Fecha</label>
                                    {{form.fecha_factura}}
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Proveedor</label>
                                    <select name="id_prov" class="form-control">
                                        <option value="">Elija Proveedor</option>
                                        {% for c in c %}
                                        <option value="{{c.nit}}">{{c.nombre}}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Piezas</label>
                                    {{form.cantidad}}
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Total</label>
                                    {{form.total}}
                                </div>

                            </div><br>

                            <div class="row">
                               
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Fecha
                                        </label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>

{% endblock %}