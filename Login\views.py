from django.shortcuts import redirect, render
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth import login, logout, authenticate
from django.contrib import messages
from django.contrib.auth.models import User


def login_in(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            usuario = form.cleaned_data.get('username')
            clave = form.cleaned_data.get('password')
            user = authenticate(username=usuario, password=clave)
            if user is not None and user.is_active:
                login(request, user)
                request.session['member_id'] = user.id
                if user.rol in ['admin', 'trabajador', 'secretaria']:
                    return redirect('Inicio')  # Redirigir a la página de inicio según el rol
                else:
                    return redirect('/')  # Redirigir a la página principal si el rol no está definido correctamente
            else:
                messages.error(request, 'Credenciales inválidas')
        else:
            messages.error(request, 'Formulario inválido')
    else:
        form = AuthenticationForm()
    
    return render(request, 'Login/login.html', {'form': form})




def logout_out(request):
   try: 
    del request.session['member_id']
    logout(request)
    messages.success(request,'Sesion Finalizada con Exito')
    return redirect('Login')
   except AttributeError:
     return redirect('/')

