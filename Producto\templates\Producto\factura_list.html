{% extends 'BaseInicio/base.html' %}
{% load static %}

{% block title %}Listado de Facturas{% endblock %}

{% block content %}
<br>
<div class="container" style="background-color: #f8f9fa; margin-top: 10px; max-width: 90%;">
    <div class="col-md-14">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="mb-0">Listado de Facturas</h5>
                <small class="text-muted float-end">Gestión de Facturas</small>
            </div>
            <div class="card-body">
                <div class="table-responsive-sm">
                    <input class="form-control col-md-3 light-table-filter" data-table="order-table" 
                           type="text" placeholder="Buscar.." style="border: 1px solid black;" 
                           name="buscar" autofocus="buscar">
                    <br>
                    <table class="table table-bordered order-table">
                        <thead>
                            <tr>
                                <th>Factura</th>
                                <th>Serie</th>
                                <th>Fecha</th>
                                <th>Total</th>
                                <th>Proveedor</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for factura in facturas %}
                            <tr>
                                <td>{{ factura.factura }}</td>
                                <td>{{ factura.serie }}</td>
                                <td>{{ factura.fecha_factura }}</td>
                                <td>{{ factura.total }}</td>
                                <td>{{ factura.id_prov.nombre }}</td>
                                <td>
                                    {% if factura.detallefactura_set.exists %}
                                        <button class="btn btn-info btn-sm" data-toggle="modal" 
                                                data-target="#facturaModal" onclick="loadFacturaDetail({{ factura.pk }})">
                                            <i class="fas fa-eye"></i> Ver
                                        </button>
                                    {% else %}
                                        <button class="btn btn-danger btn-sm" onclick="confirmDelete({{ factura.pk }})">
                                            <i class="fas fa-trash"></i> 
                                        </button>
                                    {% endif %}
                                
                                    {% if factura.factura %}
                                        <a class="btn btn-warning btn-sm" href="{% url 'DetalleIngresoFactura' factura.factura %}">
                                            <i class="fas fa-edit"></i> 
                                        </a>
                                    {% else %}
                                        <span class="text-danger">Sin factura</span>
                                    {% endif %}
                                </td>
                                
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para ver los detalles de la factura -->
<div class="modal fade" id="facturaModal" tabindex="-1" aria-labelledby="facturaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="facturaModalLabel">Detalle de Factura</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Aquí se cargará el contenido de la factura -->
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(facturaId) {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Obtener el csrf_token desde el cookie
                const csrftoken = getCookie('csrftoken');
    
                fetch(`/producto/factura/${facturaId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken  // Agregar el CSRF token en los headers
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.deleted) {
                        location.reload();
                    } else {
                        Swal.fire('Error', data.error, 'error');
                    }
                });
            }
        });
    }
    
    // Función para obtener el valor del csrf_token de las cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

function loadFacturaDetail(facturaId) {
    fetch(`/producto/factura/${facturaId}/`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('modal-body').innerHTML = html;
        });
}
</script>

<!-- Script para filtro de búsqueda en la tabla -->
<script type="text/javascript">
(function (document) {
    'use strict';

    var LightTableFilter = (function (Arr) {
        var _input;

        function _onInputEvent(e) {
            _input = e.target;
            var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
            Arr.forEach.call(tables, function (table) {
                Arr.forEach.call(table.tBodies, function (tbody) {
                    Arr.forEach.call(tbody.rows, _filter);
                });
            });
        }

        function _filter(row) {
            var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
            row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        }

        return {
            init: function () {
                var inputs = document.getElementsByClassName('light-table-filter');
                Arr.forEach.call(inputs, function (input) {
                    input.oninput = _onInputEvent;
                });
            }
        };
    })(Array.prototype);

    document.addEventListener('readystatechange', function () {
        if (document.readyState === 'complete') {
            LightTableFilter.init();
        }
    });

})(document);
</script>

{% endblock %}
