from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from user.models import User
from django.http import HttpResponse
from decimal import *


@login_required
def inicio(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        if request.user.rol == "admin":
            totalusuarios = User.objects.count()
        elif  request.user.rol == "asistente":
            pass   


        return render(request, 'Inicio/inicio.html')





