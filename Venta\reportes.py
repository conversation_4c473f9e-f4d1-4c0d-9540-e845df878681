from io import BytesIO
from datetime import datetime
from reportlab.lib.units import inch, mm, cm
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
from reportlab.lib import colors
from reportlab.platypus import (
    Paragraph, Table, SimpleDocTemplate, Spacer,
    TableStyle
)
from Venta.models import Venta, Detalle

media_carta = (8.5 * inch, 5.5 * inch)

class Comprobante():
    def __init__(self, f):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(token=f)

    def run(self):
   
        self.doc = SimpleDocTemplate(
            self.buf,
            title=f"Comprobante {self.rastreo.factura}",
            pagesize=media_carta,
            topMargin=0.5 * inch
        )
        self.story = []
        #self.story.append(Spacer(0, 2.0 * cm))  # Espacio para no tapar el encabezado
        self.doc = SimpleDocTemplate(
    self.buf,
    title=f"Comprobante {self.rastreo.factura}",
    pagesize=media_carta,
    topMargin=3.0 * cm  # deja espacio suficiente arriba en todas las páginas
)

        self.crearTabla()
        self.doc.build(
            self.story,
            onFirstPage=self.numeroPaginaEncabezado,
            onLaterPages=self.numeroPaginaEncabezado
        )
        self.story.append(Spacer(0, 2.0 * cm))
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezadoCanvas(self, canvas, doc):
        canvas.saveState()
        canvas.setFont("Helvetica", 8)
        canvas.drawString(30, doc.height + doc.topMargin + 30, f"Cliente: {self.rastreo.nombre}")
        canvas.drawString(30, doc.height + doc.topMargin + 20, f"NIT: {self.rastreo.nit}")
        canvas.drawString(30, doc.height + doc.topMargin + 10, f"Dirección: {self.rastreo.direccion}")
        canvas.drawString(30, doc.height + doc.topMargin, f"Fecha: {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}")
        canvas.drawString(30, doc.height + doc.topMargin - 10, "OBSERVACIÓN:")
        canvas.restoreState()

    def numeroPaginaEncabezado(self, canvas, doc):
        self.encabezadoCanvas(canvas, doc)
        self.numeroPagina(canvas, doc)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = "Página %s" % num
        canvas.setFont("Helvetica", 8)
        canvas.drawRightString(200 * mm, 10 * mm, text)

    def crearTabla(self):
        detalles = Detalle.objects.filter(factura=self.rastreo.factura)

        data = [["Producto", "Cantidad", "Precio Unitario", "Total"]] + [
            [
                x.nombre_custom if x.nombre_custom else x.id_inventario.nombre,
                x.cantidad,
                "Q." + str(x.precio_custom if x.precio_custom else x.precio_uni),
                "Q." + str(x.total)
            ] for x in detalles
        ] + [
            ["Total de Venta", "", "", "Q." + str(self.rastreo.total)]
        ]

        table = Table(data, colWidths=[14 * cm, 2 * cm, 2 * cm, 2 * cm], repeatRows=1)
        table.setStyle(TableStyle([
    ('ALIGN', (0, 0), (-1, -1), "LEFT"),  # Alinea todo el contenido de la tabla a la izquierda.
    ('VALIGN', (-1, -1), (-1, -1), 'MIDDLE'),  # Centra verticalmente la última celda.
    ('FONTSIZE', (0, 0), (-1, -2), 7),  # Establece el tamaño de la fuente a 7 para todas las celdas, excepto la última fila.
    ('GRID', (0, 0), (-1, -1), 0.5, colors.gray)  # Aplica un borde de 0.5 puntos en color gris alrededor de todas las celdas.
]))


        self.story.append(table)

    def estiloPC(self):
        return ParagraphStyle(name='centrado',
                              fontName="Helvetica-Bold",
                              fontSize=8,
                              alignment=TA_CENTER,
                              spaceAfter=7)

    def estiloPC2(self):
        return ParagraphStyle(
            name='izquierda',
            fontName="Helvetica",
            fontSize=9,
            leading=10,
            alignment=TA_LEFT,
            spaceAfter=1,
            leftIndent=0,
        )

    def estiloPC3(self):
        return ParagraphStyle(name='derecha',
                              fontName="Helvetica",
                              fontSize=8,
                              alignment=TA_RIGHT,
                              spaceAfter=2)



from io import BytesIO
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import ParagraphStyle
from reportlab.platypus import (
    SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
)
from reportlab.lib.units import cm, mm, inch
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
from .models import Venta, Detalle  # Ajusta la importación a tu proyecto


# Definir tamaño media carta
media_carta = (letter[0], letter[1] / 2)

class Cotizacion:
    def __init__(self, token):
        self.buf = BytesIO()
        self.rastreo = Venta.objects.get(token=token)

    def run(self):
        # Configurar documento
        self.doc = SimpleDocTemplate(
            self.buf,
            title=f"Cotizacion {self.rastreo.factura}",
            pagesize=media_carta,
            topMargin=3.0 * cm
        )
        self.story = []
        self.crearTabla()
        self.doc.build(
            self.story,
            onFirstPage=self.numeroPaginaEncabezado,
            onLaterPages=self.numeroPaginaEncabezado
        )
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezadoCanvas(self, canvas, doc):
        # Logo
        canvas.drawImage(
            'Venta/logo.jpg',
            x=doc.width + doc.leftMargin - 100,
            y=doc.height + doc.topMargin,
            width=95,
            height=85,
            preserveAspectRatio=True
        )

        canvas.saveState()
        canvas.setFont("Helvetica", 10)

        # Datos estáticos (derecha)
        canvas.drawString(330, doc.height + doc.topMargin + 20, "Avenida Selma Alabama 5-50 Z.1 Zacapa Tel: 7941-4049")
        canvas.drawString(380, doc.height + doc.topMargin + 10, "Silvia Karina Oliva Vargas de Orellana")
        canvas.drawString(420, doc.height + doc.topMargin, "Nit: 30871360")

        # Datos cliente (izquierda)
        canvas.drawString(30, doc.height + doc.topMargin + 30, f"CLIENTE: {self.rastreo.nombre}")
        canvas.drawString(30, doc.height + doc.topMargin + 20, f"NIT: {self.rastreo.nit}")

        # DIRECCIÓN con ajuste automático si es larga
        direccion = f"DIRECCION: {self.rastreo.direccion}"
        text = canvas.beginText(30, doc.height + doc.topMargin + 10)
        text.setFont("Helvetica", 10)
        max_width = 250
        palabras = direccion.split()
        linea = ""
        for palabra in palabras:
            if canvas.stringWidth(linea + palabra, "Helvetica", 10) < max_width:
                linea += palabra + " "
            else:
                text.textLine(linea.strip())
                linea = palabra + " "
        if linea:
            text.textLine(linea.strip())

        canvas.drawText(text)

        # FECHA y demás debajo de dirección
        canvas.drawString(30, text.getY() - 0, f"FECHA: {datetime.today().strftime('%d/%m/%Y, %H:%M:%S')}")
        canvas.drawString(500, doc.height + doc.topMargin - 10, f"COTIZACION: {self.rastreo.factura}")
       # canvas.drawString(30, doc.height + doc.topMargin - 10, "OBSERVACIÓN:")
        canvas.restoreState()

    def numeroPaginaEncabezado(self, canvas, doc):
        self.encabezadoCanvas(canvas, doc)
        self.numeroPagina(canvas, doc)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        text = f"Página {num}"
        canvas.setFont("Helvetica", 8)
        canvas.drawRightString(200 * mm, 10 * mm, text)

    def crearTabla(self):
        detalles = Detalle.objects.filter(factura=self.rastreo.factura)

        # Encabezados
        data = [[
            Paragraph("PRODUCTO", self.estiloPC()),
            Paragraph("CANTIDAD", self.estiloPC()),
            Paragraph("PRECIO UNITARIO", self.estiloPC()),
            Paragraph("TOTAL", self.estiloPC())
        ]]

        # Filas de detalle
        for x in detalles:
            data.append([
                Paragraph(x.nombre_custom if x.nombre_custom else x.id_inventario.nombre, self.estiloPC2()),
                Paragraph(str(x.cantidad), self.estiloPC()),
                Paragraph(f"Q.{x.precio_custom if x.precio_custom else x.precio_uni}", self.estiloPC()),
                Paragraph(f"Q.{x.total}", self.estiloPC())
            ])

        # Fila de total
        data.append([
            Paragraph("TOTAL DE VENTA", self.estiloPC()),
            '',
            '',
            Paragraph(f"Q.{self.rastreo.total}", self.estiloPC())
        ])

        # Crear tabla
        table = Table(
            data,
            colWidths=[11 * cm, 2.5 * cm, 3 * cm, 3 * cm],
            repeatRows=1
        )

        # Estilos
        table.setStyle(TableStyle([
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTSIZE', (0, 0), (-1, -2), 9),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.gray),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey)
        ]))

        self.story.append(table)

    # Estilos de párrafo
    def estiloPC(self):
        return ParagraphStyle(
            name='centrado',
            fontName='Helvetica-Bold',
            fontSize=8,
            alignment=TA_CENTER,
            spaceAfter=7
        )

    def estiloPC2(self):
        return ParagraphStyle(
            name='izquierda',
            fontName='Helvetica',
            fontSize=9,
            leading=10,
            alignment=TA_LEFT,
            spaceAfter=1
        )

    def estiloPC3(self):
        return ParagraphStyle(
            name='derecha',
            fontName='Helvetica-Bold',
            fontSize=8,
            alignment=TA_CENTER,
            spaceAfter=2
        )