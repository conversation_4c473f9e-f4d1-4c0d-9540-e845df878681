{% extends 'BaseInicio/base.html' %}
{% block title %}Lista de Productos{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>
    
    {{form.errors}}
    {% if user.rol == "admin" %}    
    <a href="{% url 'ListaProducto' %}"><button class="btn btn-success">Listado de Productos</button></a><br></br>
    <div class="container-xxl flex-grow-1 container-p-y">
        
        <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario de Actualizacion</h5>
                        <small class="text-muted float-end">Productos</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Codigo</label>
                                    {{form.codigo}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Nombre</label>
                                    {{form.nombre}}
                                </div>

                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Categoria</label>
                                    {{form.id_cate}}
                                </div>

                            </div><br>

                            <div class="row">
                                <div class="col-md-8">
                                    <label class="col-md-4 col-form-label"
                                        for="basic-default-company">Descripcion</label>
                                    {{form.descripcion}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Stock</label>
                                    <input type="text" class="form-control" value="{{ form.stock.value }}" readonly style="color: black; border: 1px solid;">
                                </div>
                                                         
                            </div><br>


                            <div class="row">
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Ingreso</label>
                                    {{form.ingreso}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-name">Compra</label>
                                    {{form.precio_compra}}
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">
                                        Venta</label>
                                    {{form.precio_venta}}
                                </div>

                            </div><br>

                            <br>
                            <div class="row">
                               
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Fecha
                                        </label>
                                    <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                                </div>
                                <div class="col-md-4">
                                    <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                    <input type="text" class="form-control" readonly value="{{user.username}}" />
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>
    {% else %}
    <a href="{% url 'ListaProducto2' %}"><button class="btn btn-success">Listado de Productos</button></a><br></br>
    <div class="row">
        
        <!-- Basic Layout -->
      
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario de Actualizacion</h5>
                    <small class="text-muted float-end">Productos</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                        <div class="row">
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-name">Codigo</label>
                                {{form.codigo}}
                            </div>
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-name">Nombre</label>
                                {{form.nombre}}
                            </div>

                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-company">Categoria</label>
                                {{form.id_cate}}
                            </div>

                        </div><br>

                        <div class="row">
                            <div class="col-md-8">
                                <label class="col-md-4 col-form-label"
                                    for="basic-default-company">Descripcion</label>
                                {{form.descripcion}}
                            </div>
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-name">Stock</label>
                                <input type="text" class="form-control" value="{{ form.stock.value }}" readonly style="color: black; border: 1px solid;">
                            </div>
                                                     
                        </div><br>


                        <div class="row">
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-name">Ingreso</label>
                                {{form.ingreso}}
                            </div>
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-name">Compra</label>
                                {{form.precio_compra}}
                            </div>
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-company">
                                    Venta</label>
                                {{form.precio_venta}}
                            </div>

                        </div><br>

                        <br>
                        <div class="row">
                           
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-company">Fecha
                                    </label>
                                <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                            </div>
                            <div class="col-md-4">
                                <label class="col-md-4 col-form-label" for="basic-default-company">Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>

                        </div><br>

                        <div class="row justify-content-end">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Guardar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

</div>

    {% endif %}

</div>

{% endblock %}