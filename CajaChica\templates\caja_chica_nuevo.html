{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container mt-4">
    {% if registro_existente %}
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                icon: 'warning',
                title: 'Registro ya existente',
                text: 'Ya has registrado la caja chica para hoy.',
                confirmButtonText: 'Aceptar'
            }).then(() => {
                window.location.href = '{% url "caja_chica_lista" %}';
            });
        </script>
    {% else %}
        <div class="card">
            <div class="card-header">
                <h3>{% if editar %}Editar{% else %}Nuevo{% endif %} Registro de Caja Chica</h3>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit" class="btn btn-primary mt-3">Guardar</button>
                </form>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
