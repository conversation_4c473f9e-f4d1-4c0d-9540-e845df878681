{% extends 'BaseInicio/base.html' %}
{% block title %}Venta{% endblock %}
{% block carta %}<i class="fa-solid fa-house"></i>Cobro{% endblock %}
{% load static %}
{% block content %}

<br>
  
<br>
<div class="container container-venta">
    <div class="card">
      <div class="card-body">
        <h4 class="card-title">Datos Venta</h4>
        <p class="card-description">
          Fecha Venta <strong>{% now 'd-m-Y' %}</strong>
        </p>
        <form class="forms-sample" method="POST" enctype="multipart/form-data">{% csrf_token %}
          <div class="row">
            <div class="col-md-4">
                    <label for="">Venta</label>
                    <input type="text" name="factura" value="{{v.factura}}" class="form-control" readonly>
                </div>
                <div class="col-md-4">
                  <label for="">Tipo de Venta</label>
                  <input type="text" name="tipo" value="Contado" class="form-control" readonly> 
            
              </div>
              <div class="col-md-4">
                    <label for="">Nit de Cliente</label>
                    <input type="text" name="nit" value="{{v.nit}}" placeholder="Nit de Cliente" class="form-control" readonly>  
                </div>
          </div><br>
          <div class="row">
            <div class="col-md-4">
                <label for="">Nombre del Cliente</label>
                <input type="text" name="nombre" value="{{v.nombre}}" class="form-control" placeholder="Nombre del Cliente" readonly>  
            </div>
            <div class="col-md-8">
                <label for="">Direccion de Cliente</label>
                <input type="text" name="direccion" value="{{v.direccion}}" class="form-control" placeholder="Direccion del Cliente" readonly>     
            </div>
          </div><br>

          <div class="row">
            <div class="col-md-4">
                <label for="">Total de Venta</label>
                <input type="text" name="total" id="total" value="{{v.total}}" class="form-control" placeholder="Nombre del Cliente" readonly>  
            </div>
       <!--     <div class="col-md-4">
                <label for="">Recibi</label>
                <input type="text" name="recibo" id="recibo" class="form-control" placeholder="0.00" required  autofocus="recibo">     
            </div>
            <div class="col-md-4">
              <label for="">Vuelto</label>
              <input type="text" name="cambio" id="cambio" class="form-control" placeholder="0.00"  readonly>     
          </div>  -->
          </div><br>

           
          <br>
          <button type="submit" class="btn btn-primary mr-2">Pagar</button>
          <button class="btn btn-light">Cancel</button>
        </form>
      </div>
    </div>
</div>

<script>
  let total = document.getElementById("total")
  let recibo = document.getElementById("recibo")
  let cambio = document.getElementById("cambio")
  
  recibo.addEventListener("change", () => {
      cambio.value = parseFloat(recibo.value) - parseFloat(total.value)

  })
  
</script>
{% endblock %}