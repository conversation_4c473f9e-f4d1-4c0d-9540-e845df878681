from decimal import Decimal
from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Proveedor.models import Proveedor
from Producto.models import Producto, ProductoFactura, DetalleFactura, Bitacora
from Producto.forms import ProductoForm, UpdateProductoForm, ProductoFacturaForm
from user.models import User
import xlwt
from django.http import HttpResponse
from django.db.models import Q
from django.db.models import Sum


@login_required
def nuevo(request):
    p = Proveedor.objects.all()
    form = ProductoFacturaForm()
    if request.method == "POST":
        if request.user.rol == "admin":
                form = ProductoFacturaForm(request.POST)
                if form.is_valid():
                    p = ProductoFactura()
                    p.factura = form.cleaned_data['factura']
                    p.serie = form.cleaned_data['serie']
                    p.fecha_factura = form.cleaned_data['fecha_factura']
                    p.cantidad = form.cleaned_data['cantidad']
                    p.total = form.cleaned_data['total']
                    p.id_prov = Proveedor.objects.get(
                        nit=request.POST['id_prov'])
                    p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    p.usuario = User.objects.get(id=request.user.id)
                    p.estado = 1
                    p.save()
                    messages.success(
                        request, f'Factura {p.factura} Ingresada!')
                    return redirect('DetalleIngresoFactura', p.factura)
        else:
            form = ProductoFacturaForm(request.POST)
            if form.is_valid():
                p = ProductoFactura()
                p.factura = form.cleaned_data['factura']
                p.serie = form.cleaned_data['serie']
                p.fecha_factura = form.cleaned_data['fecha_factura']
                p.cantidad = form.cleaned_data['cantidad']
                p.total = form.cleaned_data['total']
                p.id_prov = Proveedor.objects.get(nit=request.POST['id_prov'])
                p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                p.usuario = User.objects.get(id=request.user.id)
                p.estado = 1
                p.save()
                messages.success(request, f'Factura {p.factura} Ingresada!')
                return redirect('DetalleIngresoFactura', p.factura)

    return render(request, 'Producto/nuevofactura.html', {'form': form, 'c': p})


@login_required
def detalle(request, f):
    h = False

    if ProductoFactura.objects.filter(factura=f).exists():
        factura = ProductoFactura.objects.get(factura=f)
        detalles = DetalleFactura.objects.filter(factura=f)
        items = DetalleFactura.objects.filter(
            factura=f, estado=1).aggregate(it=Sum('cantidad'))
        tot = DetalleFactura.objects.filter(
            factura=f, estado=1).aggregate(t=Sum('total'))
            
    else:
        messages.error(
            request, 'Esta Intentando Ingresar Producto Sin Previa Factura!')
        return redirect('IngresoFactura')

    if request.method == 'POST':
 
        if 'buscar' in request.POST:

            if request.user.rol == "admin":
                
                if request.POST['tipo'] == 'nombre':
                    if Producto.objects.filter(
                            Q(nombre__icontains=request.POST['buscar']), tienda=factura.tienda).exists():
                        b = True
                    
                    else:
                        b = False
                        h = True
                        buscar = request.POST['buscar']
                
                else:
                    if Producto.objects.filter(
                            Q(id=request.POST['buscar']), tienda=request.user.tienda).exists():
                        b = True
                        buscar = Producto.objects.filter(
                            Q(id=request.POST['buscar']), tienda=request.user.tienda)
                    else:
                        b = False
                        h = True
                        buscar = request.POST['buscar']
                        
            else:
                if request.POST['tipo'] == 'nombre':
                    if Producto.objects.filter(
                            Q(nombre__icontains=request.POST['buscar']), tienda=request.user.tienda).exists():
                        b = True
                        buscar = Producto.objects.filter(
                            Q(nombre__icontains=request.POST['buscar']), tienda=request.user.tienda)
                    else:
                        b = False
                        h = True
                        buscar = request.POST['buscar']
                else:
                    if Producto.objects.filter(
                            Q(id=request.POST['buscar']), tienda=request.user.tienda).exists():
                        b = True
                        buscar = Producto.objects.filter(
                            Q(id=request.POST['buscar']), tienda=request.user.tienda)
                    else:
                        b = False
                        h = True
                        buscar = request.POST['buscar']

            return render(request, 'Producto/detallefactura.html', {'f': f, 'c': factura, 'd': detalles, 'b': b, 'buscar': buscar, 'i': items['it'], 'total': tot['t'],'h':h,})

        elif 'hacer' in request.POST:
            p = Producto()
            p.nombre = request.POST['nuevo']
            p.descripcion = 'S/D'
            p.stock = int(0)
            p.precio_compra = Decimal(0.00)
            p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
            p.usuario = User.objects.get(id=request.user.id)
            p.save()
            bitacora(f, 0,p.nombre, 'Producto No Existia', 0, p.stock,
                     p.stock, 0, p.stock, factura.tienda, request.user.username)
            messages.success(request, f'Producto {p.nombre} Ingresado!')
            return redirect('DetalleIngresoFactura', f)

        elif 'agregar' in request.POST:
            try:
                prod = Producto.objects.get(id=request.POST['id'])
                d = DetalleFactura()
                d.factura = ProductoFactura.objects.get(factura=f)
                d.id_prod = Producto.objects.get(id=prod.id)
                d.compra_antes = prod.precio_compra
                d.compra_ahora = Decimal(request.POST['nuevocompra'])
                d.venta_antes = prod.precio_venta
                d.venta_ahora = Decimal(request.POST['nuevoventa'])
                d.stock_antes = prod.stock
                d.stock_ahora = prod.stock+int(request.POST['cantidad'])
                d.cantidad = int(request.POST['cantidad'])
                d.total = d.cantidad*d.compra_ahora
                d.fecha = datetime.today()
                d.usuario = User.objects.get(id=request.user.id)
                d.estado = 1
                d.save()
                bitacora(f, prod.id,d.id_prod.nombre, 'Compra Factura', 0, d.stock_antes,
                     d.cantidad, 0, d.stock_ahora, d.factura.tienda, request.user.username)
                Producto.objects.filter(id=prod.id).update(
                    stock=prod.stock+d.cantidad, ingreso=prod.ingreso+d.cantidad, precio_compra=d.compra_ahora, precio_venta=d.venta_ahora)
                messages.success(
                    request, f'Se Ingreso {d.cantidad} de Producto {d.id_prod.nombre}!')
                return redirect('DetalleIngresoFactura', f)
            except:
                messages.error(
                    request, f'Error Al Ingresar {d.id_prod.nombre} Revise Datos!')
                return redirect('DetalleIngresoFactura', f)

        elif 'quitar' in request.POST:
            try:
                datos = DetalleFactura.objects.get(
                    id=request.POST['corr'], factura=f)
                prod = Producto.objects.get(id=datos.id_prod.id)
                Producto.objects.filter(id=datos.id_prod.id).update(stock=prod.stock-datos.cantidad, ingreso=prod.ingreso -
                                                                    datos.cantidad, precio_compra=datos.compra_antes, precio_venta=datos.venta_antes)
                bitacora(f, prod.id,datos.id_prod.nombre, 'Producto Quitado de Compra Factura', 0, datos.stock_ahora,
                     0, datos.cantidad, datos.stock_antes, datos.factura.tienda, request.user.username)
                datos.delete()
                messages.success(
                    request, f'Se Quito {datos.cantidad} de Producto {datos.id_prod.nombre} Se Actualizo Inventario!')
                return redirect('DetalleIngresoFactura', f)
            except:
                messages.error(
                   request, f'NO Se Pudo Quitar {datos.cantidad} de Producto {datos.id_prod.nombre} NO Actualizo Inventario!')
                return redirect('DetalleIngresoFactura', f)

        elif 'terminar' in request.POST:
            messages.success(
                request, f'Ingreso de Producto en Factura {f} Terminada Exitosamente!')
            return redirect('IngresoFactura')

    return render(request, 'Producto/detallefactura.html', {'f': f, 'c': factura, 'd': detalles, 'i': items['it'], 'total': tot['t'],'h':h})


@login_required
def lista(request):

    if request.user.rol == 'admin':
        f = ProductoFactura.objects.all().order_by('-fecha')
    else:
        f = ProductoFactura.objects.filter(tienda=request.user.tienda).order_by('-fecha')

    return render(request, 'Producto/listaingresos.html', {'f': f})


@login_required
def darbaja(request, f):

    if ProductoFactura.objects.filter(factura=f).exists():
        for d in DetalleFactura.objects.filter(factura=f, estado=1):
            prod = Producto.objects.get(id=d.id_prod.id)
            Producto.objects.filter(id=prod.id).update(stock=prod.stock-d.cantidad, ingreso=prod.ingreso -
                                                       d.cantidad, precio_compra=d.compra_antes, precio_venta=d.venta_antes)
            DetalleFactura.objects.filter(
                factura=f, estado=1).update(estado=99)

        ProductoFactura.objects.filter(factura=f, estado=1).update(estado=99)
        messages.success(
            request, f'Productos de Factura # {f} Fueron Dados de Baja Inventario de Estos Productos Vuelve a Precios y Stock Anterior!')
        return redirect('ListaIngresoFactura')

    else:
        messages.error(
            request, f'Error Al Dar de Baja Factura de Ingreso # {f}!')
        return redirect('ListaIngresoFactura')



def bitacora(id, id_pr, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id_pr
    b.prod = prod
    b.tipo = t
    b.doc = id
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.tienda = td
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()   