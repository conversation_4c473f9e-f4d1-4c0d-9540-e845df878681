from django.db import models
from Producto.models import ProductoFactura
from django.conf import settings
import uuid
from decimal import Decimal
from datetime import datetime

class Gasto(models.Model):
    id_gasto = models.BigAutoField(primary_key=True, blank=False, null=False)
    nombre = models.CharField(max_length=500, blank=False, null=False)
    cantidad = models.IntegerField(blank=False, null=False, default=0)
    precio_uni = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    total = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    fecha_pago = models.DateField(blank=True, null=True)
    estado = models.IntegerField(blank=False, null=False, default=1)
    fecha = models.DateTimeField(blank=True, null=True, default=datetime.today)
    usuario = models.CharField(max_length=500, blank=False, null=False)

    class Meta:
        ordering = ["id_gasto"]

    def __str__(self):
        return str(self.id_gasto)
    
    
class PagoFacturaCredito(models.Model):
    factura = models.ForeignKey(ProductoFactura, on_delete=models.CASCADE)
    tipo = models.CharField(max_length=75)
    serie = models.CharField(max_length=50, blank=True, null=True, default='')
    abono = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    fecha = models.DateField()
    usuario = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    token = models.UUIDField(default=uuid.uuid4, editable=False)

    ESTADO_PAGADA = 1
    ESTADO_CREDITO = 3
    ESTADO_CANCELADA = 4

    estado = models.IntegerField(default=ESTADO_PAGADA)  # Añade el campo estado

    ESTADOS = (
        (ESTADO_PAGADA, 'Pagada'),
        (ESTADO_CREDITO, 'A Crédito'),
        (ESTADO_CANCELADA, 'Cancelada'),
    )

    class Meta:
        ordering = ["factura"]

    def __str__(self):
        return str(self.factura)


