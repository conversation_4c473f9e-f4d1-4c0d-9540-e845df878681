from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from django.contrib import messages
from .models import CajaChica
from .forms import CajaChicaForm

def caja_chica_nuevo(request):
    # Verifica si ya existe un registro para el día actual
    hoy = timezone.now().date()
    registro_existente = CajaChica.objects.filter(fecha_creacion=hoy).first()

    if registro_existente:
        return render(request, 'caja_chica_nuevo.html', {'registro_existente': True})

    if request.method == 'POST':
        form = CajaChicaForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Registro de caja chica creado correctamente.')
            return redirect('caja_chica_lista')
    else:
        form = CajaChicaForm()

    return render(request, 'caja_chica_nuevo.html', {'form': form})

def caja_chica_lista(request):
    query = request.GET.get('fecha', '')
    if query:
        registros = CajaChica.objects.filter(fecha_creacion=query)
    else:
        registros = CajaChica.objects.all()
    return render(request, 'caja_chica_lista.html', {'registros': registros, 'query': query})


def caja_chica_editar(request, pk):
    registro = get_object_or_404(CajaChica, pk=pk)
    if request.method == 'POST':
        form = CajaChicaForm(request.POST, instance=registro)
        if form.is_valid():
            form.save()
            messages.success(request, 'Registro de caja chica actualizado correctamente.')
            return redirect('caja_chica_lista')
    else:
        form = CajaChicaForm(instance=registro)
    
    return render(request, 'caja_chica_nuevo.html', {'form': form, 'editar': True})
