{% extends 'BaseInicio/base.html' %}
{% block title %}Lista de Todas Las Ventas{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i> Lista de Todas Las Ventas {% endblock %}
{% load static %}

{% block content %}
<br>
<div class="container">
    <a href="{% url 'Venta' %}"><button class="btn btn-info">Nueva Venta</button></a>
    <br><br>

    <div class="tile">
        <h3 class="tile-title">Lista de Todas Las Ventas</h3>

        <!-- Formulario para búsqueda por fecha -->
        <form method="get" class="form-inline mb-3">
            <div class="form-group mr-2">
                <label for="fecha" class="mr-2">Buscar por fecha:</label>
                <input type="date" class="form-control" name="fecha" id="fecha" value="{{ request.GET.fecha }}">
            </div>
            <button type="submit" class="btn btn-primary">Filtrar</button>
            <a href="{% url 'ListaVentaTodas' %}" class="btn btn-secondary ml-2">Limpiar</a>
        </form>

        <!-- Input búsqueda rápida por nombre, factura, etc. -->
        <input id="buscarInput" class="form-control light-table-filter" data-table="order-table" type="text"
               placeholder="Buscar..." style="border: 1px solid black;" name="buscar" autofocus>

        {% if messages %}
            {% for message in messages %}
                <script>
                    Swal.fire({
                        "title": "Información del Sistema",
                        "text": "{{ message }}",
                        "icon": "{{ message.tags }}"
                    });
                </script>
            {% endfor %}
        {% endif %}

        <!-- Tabla -->
        <div class="table-responsive">
            <table class="table table-bordered order-table">
                <thead>
                    <tr>
                        <th>Venta #</th>
                        <th>Nombre de Cliente</th>
                        <th>Total</th>
                        <th>Estado</th>
                        <th>Tipo</th>
                        <th>Fecha</th>
                        <th>Usuario</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for p in todas %}
                    <tr>
                        <td>{{ p.factura }}</td>
                        <td>{{ p.nombre }}</td>
                        <td>Q.{{ p.total }}</td>
                        <td>
                            {% if p.estado == 1 %} Terminada
                            {% elif p.estado == 2 %} Anulada
                            {% else %} Pendiente/Sin Terminar
                            {% endif %}
                        </td>
                        <td>
                            {% if p.tipo == "Contado" %}
                                <span class="badge badge-info">FEL</span>
                            {% elif p.tipo == "Proforma" %}
                                <span class="badge badge-success">Proforma</span>
                            {% else %}
                                <span class="badge badge-warning">Cotización</span>
                            {% endif %}
                        </td>
                        
                        
                        <td>{{ p.fecha|date:"d-m-Y" }}</td>
                        <td>{{ p.usuario.username }}</td>
                        <td>
    {% if p.estado == 2 %}
        <a href="{{ p.link }}" target="_blank">
            <button class="btn btn-danger btn-sm">
                <i class="fa fa-file-pdf"><b>-Anulada</b></i>
            </button>
        </a>
    {% else %}
        {% if p.tipo != "Contado" %}
            <a href="{% url 'Detalle' p.token %}">
                <button class="btn btn-warning btn-sm"><i class="fa fa-pencil" title="Modificar"></i></button>
            </a>
        {% endif %}
        {% if p.tipo == "Contado" %}
            <a href="{{ p.link }}" target="_blank">
                <button class="btn btn-info btn-sm"><i class="fa fa-file-pdf"><b>FEL</b></i></button>
            </a>
            <a href="{% url 'AnulaFEL' p.token %}">
                <button class="btn btn-danger btn-sm"><i class="fa fa-trash">-Anular</i></button>
            </a>
        {% else %}
            <a href="{% url 'PDF' p.token %}">
                <button class="btn btn-success btn-sm"><i class="fa fa-file-pdf"><b></b></i></button>
            </a>
            <a href="{% url 'Anular' p.token %}">
                <button class="btn btn-danger btn-sm"><i class="fa fa-trash"></i></button>
            </a>
        {% endif %}
    {% endif %}
</td>

                    </tr>
                    {% empty %}
                    <tr><td colspan="7" class="text-center">No hay ventas para mostrar.</td></tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Filtro de tabla en vivo -->
<script>
    (function (document) {
        'use strict';
        var LightTableFilter = (function (Arr) {
            var _input;
            function _onInputEvent(e) {
                _input = e.target;
                var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                Arr.forEach.call(tables, function (table) {
                    Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                    });
                });
            }
            function _filter(row) {
                var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
            }
            return {
                init: function () {
                    var inputs = document.getElementsByClassName('light-table-filter');
                    Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                    });
                }
            };
        })(Array.prototype);
        document.addEventListener('readystatechange', function () {
            if (document.readyState === 'complete') {
                LightTableFilter.init();
            }
        });
    })(document);
</script>

{% endblock %}
