from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from datetime import datetime
from django.contrib import messages
from Producto.models import Producto
from Venta.models import Venta,Detalle,Gasto
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum
from django.http import HttpResponse
from .reportes import Comprobante,Cotizacion
from .forms import GastoForm
from decimal import Decimal, InvalidOperation
from Cliente.models import Cliente  # Importa el modelo Cliente desde la aplicación de clientes
from .models import Venta
from django.utils.timezone import localdate
from datetime import date

from datetime import datetime, timedelta

def ventashoy(request):
    hoy_inicio = datetime.combine(date.today(), datetime.min.time())  # Inicio del día
    hoy_fin = hoy_inicio + timedelta(days=1)  # Fin del día

    ventas_hoy = Venta.objects.filter(fecha__gte=hoy_inicio, fecha__lt=hoy_fin).order_by('-fecha')

    return render(request, 'Venta/ventashoy.html', {'hoy': ventas_hoy})




@login_required
def todasventas(request):
    todas = Venta.objects.filter(estado__lte=2).order_by('-factura','-fecha')  # Ordenar por fecha descendente
    return render(request, 'Venta/todasventas.html', {'todas': todas})

@login_required
def todasventas2(request):
    todas = Venta.objects.filter(estado__lte=2).order_by('-fecha')  # Ordenar por fecha descendente
    return render(request, 'Venta/todasventas2.html', {'todas': todas})

@login_required
def lista_ventas_pagadas(request):
    # Filtrar las ventas que están pagadas, a crédito o canceladas
    ventas_pagadas = Venta.objects.filter(
        estado__in=[Venta.ESTADO_PAGADA, Venta.ESTADO_CANCELADA]
    ).order_by('-fecha')
    return render(request, 'Venta/ventas_pagadas.html', {'ventas_pagadas': ventas_pagadas})



cont = 0
@login_required
def venta(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        ultima = Venta.objects.order_by('factura').last()
        if ultima is not None:
            nueva = ultima.factura + 1
        else:
            nueva = 1  # Asigna un valor inicial si no hay ventas previas

        if request.method == "POST":
            cliente = Cliente.objects.filter(nit=request.POST["nit"]).exists()
            
            if cliente:
                c = Cliente.objects.get(nit=request.POST["nit"])
                nit = c.nit
                nombre = c.nombre
                direccion = c.direccion
            else:
                nit = 'CF'
                nombre = 'Consumidor Final'
                direccion = 'Ciudad'

            v = Venta()
            v.nit = nit
            v.nombre = nombre
            v.direccion = direccion
            v.estado = 0
            v.usuario = request.user  # Asigna el objeto User completo
            v.fecha = datetime.today()
            v.save()
            if v.tipo == 'Cotizacion':
                messages.success(request, 'Cotizacion Iniciada')
                return redirect("VCotizacion", v.token)
            else:
                messages.success(request, 'Venta Iniciada')
                return redirect("Detalle", v.token)
        else:
            pass

    return render(request, "Venta/venta.html", {'v': nueva})

@login_required
def detalle(request, f):
    venta = Venta.objects.get(token=f)
    det = Detalle.objects.filter(token=f)
    b = False

    if venta:
        if request.method == "POST":
            # Buscar productos
            if 'buscar' in request.POST:
                if request.POST['buscar'] == "":
                    messages.error(request, 'Campo No Puede Estar Vacio')
                    return redirect('Detalle', f)
                else:
                    # Crear el filtro para buscar por codigo o nombre
                    querys = (Q(codigo__icontains=request.POST['buscar']) | Q(nombre__icontains=request.POST['buscar']))
                    # Filtrar y ordenar los productos que coinciden con la búsqueda alfabéticamente por nombre
                    busqueda = Producto.objects.filter(querys).order_by('nombre')  # Ordenar por 'nombre'
                    b = True
                    # Renderizar la plantilla con los productos encontrados
                    return render(request, 'Venta/detalle.html', {
                        'venta': venta,
                        'detalle': det,
                        'busq': busqueda,
                        'b': b
                    })

            # Agregar productos a la venta
            elif 'agregar' in request.POST:
                # Asegurarte de que 'existencia' esté definida antes de usarla
                if Producto.objects.filter(id=request.POST['id']).exists():
                    existencia = Producto.objects.get(id=request.POST['id'])

                    # Verificar existencia en stock
                    if existencia.stock < int(request.POST['cantidad']):
                        messages.error(request, f'{existencia.codigo} No Tiene Existencia!')
                        return redirect('Detalle', f)
                    else:
                        # Crear una nueva fila en Detalle para cada instancia del producto
                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_inventario = Producto.objects.get(id=existencia.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio_uni = existencia.precio_venta

                        # Verificar si 'descuento' es un valor válido antes de convertirlo a Decimal
                        descuento_str = request.POST.get('descuento', '0').strip()  # Si está vacío, será '0'
                        try:
                            descuento = Decimal(descuento_str)  # Intentar convertir a Decimal
                        except (InvalidOperation, ValueError):
                            descuento = Decimal(0)  # Si no es válido, asignar 0
            
                        d.descuento = d.cantidad * descuento
                        d.subtotal = d.cantidad * d.precio_uni
                        d.total = d.subtotal - d.descuento
                        d.estado = 1
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = f
                        d.save()
                        
                        # Actualizar el stock del producto
                        existencia.stock -= d.cantidad
                        existencia.save()
                        
                        # Actualizar total de la venta
                        venta.total += (d.subtotal - d.descuento)
                        venta.save()
                        
                        messages.success(request, f'Agregando {request.POST["cantidad"]} Unidad de {existencia.nombre}')
                        return redirect('Detalle', f)

            # Quitar productos de la venta
            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(id=request.POST['corr'], token=f)
                elprod = Producto.objects.get(id=idetalle.id_inventario.id)
                
                # Actualizar total de la venta
                venta.total -= idetalle.total
                venta.save()
                
                Producto.objects.filter(id=idetalle.id_inventario.id).update(
                    stock=elprod.stock + idetalle.cantidad, salio=elprod.salio - idetalle.cantidad
                )
                idetalle.delete()
                
                messages.success(request, 'Producto quitado')
                return redirect('Detalle', f)

            # Aplicar descuento total
            elif 'descuento_total' in request.POST:
                descuento = Decimal(request.POST.get('descuento_total', 0))
                venta.descuento_total += descuento
                venta.total -= descuento
                venta.save()
                
                messages.success(request, "Descuento aplicado correctamente.")
                return redirect('Detalle', f)

            # Editar producto en la venta
            elif 'nombre_producto' in request.POST:
                corr = request.POST.get('corr')
                detalle = Detalle.objects.get(id=corr)
                
                nuevo_nombre = request.POST.get('nombre_producto')
                if nuevo_nombre != detalle.id_inventario.nombre:
                    detalle.nombre_custom = nuevo_nombre

                nuevo_precio_str = request.POST.get('precio_producto', '0')
                nuevo_precio = Decimal(nuevo_precio_str.replace(',', '.'))
                if nuevo_precio != detalle.precio_uni:
                    detalle.precio_custom = nuevo_precio
                    detalle.precio_uni = nuevo_precio

                # Recalcular el subtotal y el total del detalle
                detalle.subtotal = detalle.cantidad * detalle.precio_uni
                detalle.total = detalle.subtotal - detalle.descuento
                detalle.save()

                # Recalcular el total de la venta
                venta.total = Detalle.objects.filter(factura=venta.factura, token=f).aggregate(total=Sum('total'))['total'] or 0
                venta.save()

                messages.success(request, "Producto editado correctamente.")
                return redirect('Detalle', f)

            # Finalizar venta
            elif 'terminar' in request.POST:
                if 'credito' in request.POST:
                    venta.estado = 3
                else:
                    venta.estado = 1

                venta.nit = request.POST['nit']
                venta.nombre = request.POST['nombre']
                venta.direccion = request.POST['direccion']
                venta.save()
                messages.success(request, f'Venta {f} finalizada')
                return redirect('Venta')

    else:
        messages.error(request, 'Venta Terminada Token Corrupto')
        return redirect('Venta')

    subtalVenta = venta.descuento_total + venta.total
    return render(request, 'Venta/detalle.html', {'venta': venta, 'detalle': det, 'b': b, 'subtalVenta': subtalVenta})




@login_required
def listacotizacion(request):
    coti = Venta.objects.filter(estado=3)
    return render(request,'Venta/cotizacion.html',{'coti':coti})


@login_required
def cotizacion(request):
        v = Venta()
        v.nit = 'CF'
        v.nombre = "Consumidor Final"
        v.direccion = "Ciudad"   
        v.total = 0.00
        v.estado = 0
        v.fecha = datetime.today().strftime('%Y-%m-%d')
        v.usuario = User.objects.get(id=request.user.id)
        v.token = uuid.uuid4()
        v.save()
        messages.success(request,'Cotizacion Iniciada')
        return redirect('DetalleCotizacion',v.token)


@login_required
def detallecotizacion(request,f):

    venta = Venta.objects.get(token=f)
    det = Detalle.objects.filter(token=f)
    totales = Detalle.objects.filter(factura=venta.factura,token=f).aggregate(tot=Sum('total'))
    if venta:
        if request.method == "POST":
          
            if 'buscar' in request.POST:
              if request.POST['buscar'] == "":
                messages.error(request,f'Campo No Puede Estar Vacio')
                return redirect('DetalleCotizacion',f)
              else:  

                if Producto.objects.filter(codigo=request.POST['buscar']).exists():
                    existencia = Producto.objects.get(codigo=request.POST['buscar']) 

                        # ver si esta en detalles
                    if Detalle.objects.filter(id_inventario=existencia.id,token=f).exists():
                        endetalle = Detalle.objects.get(id_inventario=existencia.id,token=f)
                        Detalle.objects.filter(id_inventario=existencia.id,token=f).update(cantidad=endetalle.cantidad+1,total=endetalle.total+existencia.precio_venta)
                        Venta.objects.filter(factura=venta.factura,token=f).update(total=venta.total+existencia.precio_venta)
                        messages.success(request,f'Agregando 1 Unidad de {existencia.nombre}')
                        return redirect('DetalleCotizacion',f)
                    else:
                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_inventario = Producto.objects.get(id=existencia.id)
                        d.cantidad = 1
                        d.precio_uni = existencia.precio_venta
                        d.descuento = 0.00
                        d.subtotal = 0.00 
                        d.total = d.cantidad*existencia.precio_venta
                        d.estado = 3
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = f
                        d.save()
                        Venta.objects.filter(factura=venta.factura,token=f).update(total=venta.total+(1*existencia.precio_venta))
                        messages.success(request,f'Agregando 1 Unidad de {existencia.nombre}')
                        return redirect('DetalleCotizacion',f)

                    


                # si no coincide el codigo buscar por coincidencia escrita
                else:
                    querys = (Q(codigo__icontains=request.POST['buscar'].strip()) | Q(nombre__icontains=request.POST['buscar'].strip()))
                    busqueda = Producto.objects.filter(querys)
                    return render(request,'Venta/detallecotizacion.html',{'venta':venta,'detalle':det,'b':busqueda})
            
            elif 'agregar' in request.POST:

                if Producto.objects.filter(id=request.POST['id']).exists():
                    existencia = Producto.objects.get(id=request.POST['id']) 

                    # ver si esta en detalles
                    if Detalle.objects.filter(id_inventario=existencia.id,token=f).exists():
                        endetalle = Detalle.objects.get(id_inventario=existencia.id,token=f)
                          
                        Detalle.objects.filter(id_inventario=existencia.id,token=f).update(cantidad=endetalle.cantidad+int(request.POST['cantidad']),total=endetalle.total+((existencia.precio_venta*int(request.POST['cantidad'])))-Decimal(request.POST['descuento']))
                                                      
                        Venta.objects.filter(factura=venta.factura,token=f).update(total=venta.total+((int(request.POST['cantidad'])*existencia.precio_venta))-Decimal(request.POST['descuento']))
                            
                        messages.success(request,f'Agregando {request.POST["cantidad"]} Unidad de {existencia.nombre}')
                        return redirect('DetalleCotizacion',f)
                    else:
                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_inventario = Producto.objects.get(id=existencia.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio_uni = existencia.precio_venta
                        d.descuento = Decimal(request.POST['descuento'])
                        d.subtotal = d.cantidad*d.precio_uni 
                        d.total = (d.cantidad*d.precio_uni)-Decimal(request.POST['descuento'])
                        d.estado = 3
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = f
                        d.save()
                        Venta.objects.filter(factura=venta.factura,token=f).update(total=venta.total+((int(request.POST['cantidad'])*existencia.precio_venta))-Decimal(request.POST['descuento']))
                        messages.success(request,f'Agregando {request.POST["cantidad"]} Unidad de {existencia.nombre}')
                        return redirect('DetalleCotizacion',f)
                        
            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(id=request.POST['corr'],token=f)
                elprod = Producto.objects.get(id=idetalle.id_inventario.id)
                Venta.objects.filter(factura=venta.factura,token=f).update(total=venta.total-((idetalle.cantidad*idetalle.precio_uni)-idetalle.descuento))
                idetalle.delete()
                messages.success(request,f'Quitado')
                return redirect('DetalleCotizacion',f)
            
            elif 'terminar' in request.POST:
                Venta.objects.filter(token=f).update(nit=request.POST['nit'],nombre=request.POST['nombre'],direccion=request.POST['direccion'],estado=3)
                messages.success(request,f'{f}')
                return redirect('Cotizacion') 

    
    else:
        messages.error(request,'Venta Terminada Token Corrupto')
        return redirect('Cotizacion')                

    return render(request,'Venta/detallecotizacion.html',{'venta':venta,'detalle':det})




@login_required
def gasto(request):
    form = GastoForm()
    if request.method == "POST":
        form = GastoForm(request.POST)
        if form.is_valid():
            try:
                g = Gasto()
                g.nombre = form.cleaned_data['nombre']
                g.cantidad = form.cleaned_data['cantidad']
                g.precio_uni = form.cleaned_data['precio_uni']
                g.total = g.cantidad*g.precio_uni
                g.estado = 1
                g.fecha = datetime.today().strftime('%Y-%m-%d')
                g.usuario = request.user.username
                g.save()
                messages.success(request,f'Gasto Ingresado Correctamente!')
                return redirect('Gasto')
            except:
                messages.error(request,f'Error al Ingresar Gasto!')
                return redirect('Gasto')


    return render(request,'Venta/gastos.html',{'form':form})



@login_required
def listagasto(request):
    gastos = Gasto.objects.all()
    return render(request,'Venta/todogasto.html',{'gastos':gastos})



@login_required
def listagastohoy(request):
    print(datetime.today().strftime('%Y-%m-%d'))
    gastos = Gasto.objects.filter(fecha=datetime.today().strftime('%Y-%m-%d'))
    return render(request,'Venta/todogastohoy.html',{'gastos':gastos})
    
    


@login_required
def estadisticas(request):
    
    todoventa = Venta.objects.filter(estado=1).aggregate(tv=Sum('total'))
    totalhechas = Venta.objects.filter(estado=1).count()
    
    todoventahoy = Venta.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).aggregate(tvh=Sum('total'))
    totalhechashoy = Venta.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).count()

    todogasto = Gasto.objects.aggregate(g=Sum('total'))
    todogastohoy = Gasto.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).aggregate(gh=Sum('total'))

    ingresos = Venta.objects.filter(estado=1).aggregate(i=Sum('total'))
    egresos = Gasto.objects.filter(estado=1).aggregate(e=Sum('total'))
    
    if todogastohoy['gh'] == None:
        todogastohoy['gh'] = Decimal(0.00)
    else:
        totalhechashoy['gh']

    if todoventahoy['tvh'] == None:
        todoventahoy['tvh'] = Decimal(0.00)
    else:
        todoventahoy['tvh']
    
    if ingresos['i'] == None:
        ingresos['i'] = Decimal(0.00)
    else:
        ingresos['i']    

    if egresos['e'] == None:
        egresos['e'] = Decimal(0.00)
    else:
        egresos['e']   
        

    li = ingresos['i']-egresos['e']
    li_h = todoventahoy['tvh']-todogastohoy['gh']


   
    return render(request,'Venta/estadistica.html',{'tdv':todoventa['tv'],'th':totalhechas,'tvh':totalhechashoy,'thh':todoventahoy['tvh'],'tdg':todogasto['g'],'tdgh':todogastohoy['gh'],'i':ingresos['i'],'e':egresos['e'],'l1':li,'l2':li_h})
    
    

@login_required
def descartar(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salio=p.salio-d.cantidad)
            else:
                pass   

    dsdetalle.delete()
    dsventa.delete()
    messages.success(request,'Venta Descartada')
    return redirect('Venta') 


@login_required
def anularventa(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salida=p.salida-d.cantidad)
            else:
                pass   

    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Venta Anulada Productos Vuelven a Stock')
    return redirect('ListaVentaHoy') 
    

@login_required
def anularventa2(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salio=p.salio-d.cantidad)
            else:
                pass   

    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Venta Anulada Productos Vuelven a Stock')
    return redirect('ListaVentaTodas') 
    


@login_required
def anularcotizacion(request,f):
    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Cotizacion Anulada')
    return redirect('Cotizacion')



def pdf(request,f):
   if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
   else: 
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="comprobante-venta-#-{f}.pdf"'
    r = Comprobante(f)
    response.write(r.run())
    return response  


def pdfcotizacion(request,f):
   if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
   else: 
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="cotizacion-#-{f}.pdf"'
    r = Cotizacion(f)
    response.write(r.run())
    return response  


###########################   PDF CREDITO  #####################################

from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from .models import Venta, Abono  # Asegúrate de tener el modelo Abono
from django.template.loader import render_to_string


@login_required
def pdf_credito(request, f):
    venta = get_object_or_404(Venta, factura=f)
    abonos = Abono.objects.filter(venta=venta)  # Asumiendo que tienes un modelo Abono relacionado
    saldo = venta.total - abonos.aggregate(Sum('monto'))['monto__sum'] if abonos.exists() else venta.total

    # Determinar el estado de la venta
    if venta.estado == Venta.ESTADO_CREDITO and saldo <= 0:
        estado = 'Cancelada'
    else:
        estado = 'Activa'  # o cualquier otro estado que desees mostrar

    # Renderizar la plantilla para el PDF
    html_string = render_to_string('Venta/pdf_credito_venta.html', {
        'venta': venta,
        'abonos': abonos,
        'saldo': saldo,
        'estado': estado,
    })

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="venta_{venta.factura}.pdf"'


    return response

