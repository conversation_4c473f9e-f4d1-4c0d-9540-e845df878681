{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Lista de Créditos FEL Pendientes</h4>
                    <p class="card-description">Total de créditos pendientes: <strong>{{ total_creditos }}</strong></p>
                    
                    <!-- Buscador -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchInput" class="form-control" placeholder="Buscar por número de venta, cliente, NIT o fecha...">
                        </div>
                        <div class="col-md-6">
                            <button id="searchBtn" class="btn btn-primary">Buscar</button>
                            <button id="clearBtn" class="btn btn-secondary">Limpiar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <!-- Tabla -->
                    <div class="table-responsive">
                        <table class="table table-bordered order-table">
                            <thead>
                                <tr>
                                    <th>Venta #</th>
                                    <th>Nombre de Cliente</th>
                                    <th>NIT</th>
                                    <th>Total</th>
                                    <th>Estado</th>
                                    <th>Tipo</th>
                                    <th>Fecha</th>
                                    <th>Usuario</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for credito in creditos %}
                                <tr>
                                    <td>{{ credito.factura }}</td>
                                    <td>{{ credito.nombre }}</td>
                                    <td>{{ credito.nit }}</td>
                                    <td>Q.{{ credito.total }}</td>
                                    <td><span class="badge badge-warning">No pagado</span></td>
                                    <td><span class="badge badge-info">Credito FEL</span></td>
                                    <td>{{ credito.fecha|date:"d/m/Y" }}</td>
                                    <td>{{ credito.usuario.username }}</td>
                                    <td>
                                        <!-- Botón para pagar crédito -->
                                        <a href="{% url 'pagar_credito_fel' credito.token %}">
                                            <button class="btn btn-success btn-sm">
                                                <i class="fa fa-money"></i> Pagar
                                            </button>
                                        </a>
                                        
                                        <!-- Botón FEL -->
                                        {% if credito.link %}
                                            <a href="{{ credito.link }}" target="_blank">
                                                <button class="btn btn-info btn-sm">
                                                    <i class="fa fa-file-pdf"></i> <b>FEL</b>
                                                </button>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-info btn-sm" disabled>FEL no disponible</button>
                                        {% endif %}
                                        
                                        <!-- Botón ver detalles -->
                                        <a href="{% url 'detalle_credito_fel' credito.token %}">
                                            <button class="btn btn-primary btn-sm">
                                                <i class="fa fa-eye"></i> Ver
                                            </button>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center">No hay créditos FEL pendientes</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Loading indicator -->
                    <div id="loading" class="text-center" style="display: none;">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Cargando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lazy Loading y Filtro dinámico -->
<script>
    let offset = 25; // Empezamos desde 25 porque ya cargamos los primeros 25
    let limit = 25;
    let loading = false;
    let searchTerm = '';
    let usingAjaxSearch = false;

    function cargarCreditos(reset = false) {
        if (loading) return;
        loading = true;

        const params = new URLSearchParams({
            offset: reset ? 0 : offset,
            limit: limit,
            search: searchTerm
        });

        fetch(`?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(res => res.json())
        .then(data => {
            const tbody = document.querySelector('.order-table tbody');
            if (reset) {
                tbody.innerHTML = '';
                offset = 0;
            }

            data.creditos.forEach(credito => {
                const row = document.createElement('tr');

                // Botón FEL
                let botonFel = '';
                if (credito.link) {
                    botonFel = `<a href="${credito.link}" target="_blank">
                                  <button class="btn btn-info btn-sm">
                                    <i class="fa fa-file-pdf"></i> <b>FEL</b>
                                  </button>
                                </a>`;
                } else {
                    botonFel = `<button class="btn btn-info btn-sm" disabled>FEL no disponible</button>`;
                }

                row.innerHTML = `
                    <td>${credito.factura}</td>
                    <td>${credito.nombre}</td>
                    <td>${credito.nit}</td>
                    <td>Q.${credito.total}</td>
                    <td><span class="badge badge-warning">No pagado</span></td>
                    <td><span class="badge badge-info">Credito FEL</span></td>
                    <td>${credito.fecha}</td>
                    <td>${credito.usuario}</td>
                    <td>
                        <a href="/venta/creditos-fel/pagar/${credito.token}/">
                            <button class="btn btn-success btn-sm">
                                <i class="fa fa-money"></i> Pagar
                            </button>
                        </a>
                        ${botonFel}
                        <a href="/venta/creditos-fel/detalle/${credito.token}/">
                            <button class="btn btn-primary btn-sm">
                                <i class="fa fa-eye"></i> Ver
                            </button>
                        </a>
                    </td>
                `;
                tbody.appendChild(row);
            });

            offset += limit;
            loading = false;

            // Ocultar loading
            document.getElementById('loading').style.display = 'none';

            // Si no hay más datos, remover el event listener
            if (!data.has_more) {
                window.removeEventListener('scroll', handleScroll);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            loading = false;
            document.getElementById('loading').style.display = 'none';
        });
    }

    function handleScroll() {
        if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
            document.getElementById('loading').style.display = 'block';
            cargarCreditos();
        }
    }

    // Event listeners
    document.getElementById('searchBtn').addEventListener('click', function() {
        searchTerm = document.getElementById('searchInput').value;
        usingAjaxSearch = true;
        cargarCreditos(true);
        
        // Agregar scroll listener si no existe
        window.addEventListener('scroll', handleScroll);
    });

    document.getElementById('clearBtn').addEventListener('click', function() {
        document.getElementById('searchInput').value = '';
        searchTerm = '';
        usingAjaxSearch = false;
        location.reload();
    });

    // Enter key en search
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('searchBtn').click();
        }
    });

    // Solo agregar scroll listener si no estamos usando búsqueda inicial
    if (!usingAjaxSearch) {
        window.addEventListener('scroll', handleScroll);
    }
</script>
{% endblock %}
