from django.shortcuts import get_object_or_404, render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Cliente.models import Cliente
from Cliente.forms import ClienteForm,UpdateClienteForm
from Venta.models import Venta
from user.models import User
from django.core.paginator import Paginator

@login_required
def cliente(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        #paginacion 
        comments = Cliente.objects.all() 
        paginator = Paginator(comments, 10) # Show 25 contacts per page.
 
        page_number = request.GET.get('page')
        comments_page = paginator.get_page(page_number)

        return render(request,'Cliente/lista.html',{'comments_page': comments_page,'cliente':comments})
    
@login_required
def nuevo(request):
    form = ClienteForm()
    if request.method == "POST":
        form = ClienteForm(request.POST)
        if form.is_valid():
            try:
                c = Cliente()
                c.nit = form.cleaned_data['nit']
                c.nombre = form.cleaned_data['nombre']
                c.direccion = form.cleaned_data['direccion']
                c.tel = form.cleaned_data['tel']
                c.compras_contado = 0
                c.total_contado = 0.00
                c.compras_credito = 0
                c.total_credito = 0.00
                c.total_credito_pagado = 0.00
                c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                c.usuario = User.objects.get(id=request.user.id)
                c.save()
                messages.success(request,f'{c.nombre} Ingresado!')
                return redirect('NuevoCliente')
            except:
                messages.error(request,f'No Se Pudo Ingresar a {c.nombre}!')
                return redirect('NuevoCliente')
    
    return render(request,'Cliente/nuevo.html',{'form':form})



@login_required
def listado(request):
    datos = Cliente.objects.all().order_by('nombre')
    return render(request,'Cliente/lista.html',{'cli':datos})


@login_required
def actualizar(request,id):
    cli = Cliente.objects.get(nit=id)
    if request.method == 'GET':
        form = UpdateClienteForm(instance=cli)
    else:
        form = UpdateClienteForm(request.POST,instance = cli)
     
        if form.is_valid():
            try:
                cli.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                cli.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(request, f'Categoria {cli.nombre} Modificada Exitosamente!')
                return redirect('ListaCliente')
            except:
                messages.error(request, f'No Se Pudo Modificar {cli.nombre}!')
                return redirect('ListaCliente')

    return render(request,'Cliente/actualizar.html',{'form':form})




@login_required
def eliminar(request,id):
    try:
        cli = Cliente.objects.get(nit=id)
        cli.delete()
        messages.success(request,f'{cli.nombre} Eliminado!')
        return redirect('ListaCliente')
    except:
        messages.error(request,f'No Se Puede Eliminar {cli.nombre}')
        return redirect('ListaCliente')    






from datetime import datetime, timedelta

@login_required
def ventas_cliente(request, nit):
    cliente = get_object_or_404(Cliente, nit=nit)
    ventas = Venta.objects.filter(nit=cliente.nit).order_by('-fecha')

    # Filtrar por rango de fechas si se proporciona
    fecha_inicio = request.GET.get('fecha_inicio')
    fecha_fin = request.GET.get('fecha_fin')
    if fecha_inicio and fecha_fin:
        try:
            inicio = datetime.strptime(fecha_inicio, '%Y-%m-%d')
            # Ajustar fin para incluir el día completo
            fin = datetime.strptime(fecha_fin, '%Y-%m-%d') + timedelta(days=1) - timedelta(seconds=1)
            ventas = ventas.filter(fecha__range=(inicio, fin))
        except ValueError:
            pass  # Manejar errores de formato de fecha si es necesario

    return render(request, 'Cliente/ventas_cliente.html', {
        'cliente': cliente,
        'ventas': ventas,
        'fecha_inicio': fecha_inicio,
        'fecha_fin': fecha_fin,
    })
