{% extends 'BaseInicio/base.html' %}
{% load humanize %}

{% block content %}
<div class="container mt-4">
    <h2>Listado de Cierres de Caja</h2>
    <table class="table table-bordered table-striped mt-3">
        <thead class="thead-dark">
            <tr>
                <th>Fecha</th>
                <th>Total Caja Chica</th>
                <th>Total Ventas</th>
                <th>Total Abonos</th>
                <th>Total Pagos</th>
                <th>Total Facturas Crédito</th>
                <th>Balance</th>
            </tr>
        </thead>
        <tbody>
            {% for cierre in cierres %}
            <tr>
                <td>{{ cierre.fecha_cierre|date:"d \d\e F \d\e Y" }}</td>
                <td>Q {{ cierre.total_caja_chica|default:0|floatformat:2|intcomma }}</td>
                <td>Q {{ cierre.total_ventas|default:0|floatformat:2|intcomma }}</td>
                <td>Q {{ cierre.total_abonos|default:0|floatformat:2|intcomma }}</td>
                <td>Q {{ cierre.total_pagos|default:0|floatformat:2|intcomma }}</td>
                <td>Q {{ cierre.total_facturas_credito|default:0|floatformat:2|intcomma }}</td>
                <td>Q {{ cierre.balance|default:0|floatformat:2|intcomma }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center">No hay cierres registrados.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
