from django.db import models
from django.utils import timezone
from decimal import Decimal

class CierreCaja(models.Model):
    total_caja_chica = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_abonos = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_ventas = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_pagos = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_facturas_credito = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    pagos_creditos_fel = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_ingresos = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    total_salidas = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    balance = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    fecha_cierre = models.DateField(default=timezone.now, unique=True)

    def __str__(self):
        return f"Cierre - {self.fecha_cierre}"
