{% extends 'BaseInicio/base.html' %}
{% block title %}Lista de Todos Los Gastos{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i>Lista de Todas Los Gastos{%endblock %}
{% load static %}
{% block content %}

<div class="col-md-12">
    {% if messages %}
    {% for message in messages %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "{{message}}",
            "icon": "{{message.tags}}"
        })
    </script>
    {% endfor %}
    {% endif %}

    <div class="col-md-12 container-table">

        <a href="{% url 'Gasto' %}"><button class="btn btn-success">Nuevo Pago</button></a>
      
      </div>  
<div class="col-md-12 container-table">
  <br>
</div> 

<div class="col-md-12 container-table">
    <div class="tile">
        <h3 class=" tile-title">Lista de Todos Los Pagos</h3><br>
        <div class="row">
            <div class="col-md-12" align="right">
                <a href="{% url 'TodoGastoHoy' %}" class="btn btn-warning">Gastos Hoy</a>

        </div>
        <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar">
        <div class="table-responsive">
            <table class="table table-bordered order-table">
                <thead style="font-size: 14px;">
                    <tr>
                        <th>Gasto #</th>
                        <th>Descripcion</th>
                        <th>Cantidad</th>
                        <th>Precio Unitario</th>
                        <th>Total</th>
                        <th>Fecha Pago</th>
                        <th>Usuario</th>
                        <th>Fecha Ingreso</th>
                        <th>Accion</th>
                    </tr>
                </thead>
                <tbody style="font-size: 14px;">
                    {% for g in gastos %}
                    <tr>
                        <td>{{g.id_gasto}}</td>
                        <td>{{g.nombre}}</td>
                        <td>{{g.cantidad}}</td>
                        <td>Q.{{g.precio_uni}}</td>
                        <td>Q.{{g.total}}</td>
                        <td>{{g.fecha_pago}}</td>
                        <td>{{g.usuario}}</td>
                        <td>{{g.fecha|date:"d-m-Y"}}</td>
                        <td>
                            <a href="#"><button class="btn btn-info btn-sm"><i
                                        class="fa-solid fa-file-pdf"></i>Editar</button></a>
                            <a href="#"><button class="btn btn-danger btn-sm"><i
                                class="fa-solid fa-file-pdf"></i>Eliminar</button></a>
                        </td>
                    </tr>
                    {% empty %}
                    <caption>SIN VENTAS</caption>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>



<script type="text/javascript">
        (function (document) {
            'use strict';

            var LightTableFilter = (function (Arr) {

                var _input;

                function _onInputEvent(e) {
                    _input = e.target;
                    var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                    Arr.forEach.call(tables, function (table) {
                        Arr.forEach.call(table.tBodies, function (tbody) {
                            Arr.forEach.call(tbody.rows, _filter);
                        });
                    });
                }

                function _filter(row) {
                    var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                    row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
                }

                return {
                    init: function () {
                        var inputs = document.getElementsByClassName('light-table-filter');
                        Arr.forEach.call(inputs, function (input) {
                            input.oninput = _onInputEvent;
                        });
                    }
                };
            })(Array.prototype);

            document.addEventListener('readystatechange', function () {
                if (document.readyState === 'complete') {
                    LightTableFilter.init();
                }
            });

        })(document);
</script>

{% endblock %}