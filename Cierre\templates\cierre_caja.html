{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h3>Cierre de Caja del Día</h3>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Concepto</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Ingresos Totales</td>
                        <td>{{ total_ingresos }}</td>
                    </tr>
                    <tr>
                        <td>Salidas Totales</td>
                        <td>{{ total_salidas }}</td>
                    </tr>
                    <tr>
                        <td>Balance</td>
                        <td>{{ balance }}</td>
                    </tr>
                </tbody>
            </table>

            {% if not existe_cierre %}
                <button id="btn-cierre" class="btn btn-primary mt-3">Registrar Cierre</button>
            {% else %}
                <div class="alert alert-info mt-3">
                    El cierre de caja ya ha sido registrado para hoy.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.getElementById('btn-cierre').addEventListener('click', function () {
        Swal.fire({
            title: '¿Estás seguro de realizar el cierre?',
            text: "Esta acción no puede deshacerse.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Sí, registrar cierre',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                document.location.href = window.location.href + '?confirmar_cierre=true';
            }
        });
    });
</script>
{% endblock %}
