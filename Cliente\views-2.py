from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Cliente.models import Cliente
from Cliente.forms import ClienteForm,UpdateClienteForm
from user.models import User
from django.core.paginator import Paginator

@login_required
def cliente(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        #paginacion 
        comments = Cliente.objects.all() 
        paginator = Paginator(comments, 10) # Show 25 contacts per page.
 
        page_number = request.GET.get('page')
        comments_page = paginator.get_page(page_number)

        return render(request,'Cliente/lista.html',{'comments_page': comments_page,'cliente':comments})
    
@login_required
def nuevo(request):
    form = ClienteForm()
    if request.method == "POST":
        form = ClienteForm(request.POST)
        if form.is_valid():
            try:
                c = Cliente()
                c.nit = form.cleaned_data['nit']
                c.nombre = form.cleaned_data['nombre']
                c.direccion = form.cleaned_data['direccion']
                c.tel = form.cleaned_data['tel']
                c.compras_contado = 0
                c.total_contado = 0.00
                c.compras_credito = 0
                c.total_credito = 0.00
                c.total_credito_pagado = 0.00
                c.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                c.usuario = User.objects.get(id=request.user.id)
                c.save()
                messages.success(request,f'{c.nombre} Ingresado!')
                return redirect('NuevoCliente')
            except:
                messages.error(request,f'No Se Pudo Ingresar a {c.nombre}!')
                return redirect('NuevoCliente')
    
    return render(request,'Cliente/nuevo.html',{'form':form})



@login_required
def listado(request):
    datos = Cliente.objects.all().order_by('nit')
    return render(request,'Cliente/lista.html',{'cli':datos})


@login_required
def actualizar(request,id):
    cli = Cliente.objects.get(nit=id)
    if request.method == 'GET':
        form = UpdateClienteForm(instance=cli)
    else:
        form = UpdateClienteForm(request.POST,instance = cli)
     
        if form.is_valid():
            try:
                cli.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                cli.usuario = User.objects.get(id=request.user.id)
                form.save()
                messages.success(request, f'Categoria {cli.nombre} Modificada Exitosamente!')
                return redirect('ListaCliente')
            except:
                messages.error(request, f'No Se Pudo Modificar {cli.nombre}!')
                return redirect('ListaCliente')

    return render(request,'Cliente/actualizar.html',{'form':form})




@login_required
def eliminar(request,id):
    try:
        cli = Cliente.objects.get(nit=id)
        cli.delete()
        messages.success(request,f'{cli.nombre} Eliminado!')
        return redirect('ListaCliente')
    except:
        messages.error(request,f'No Se Puede Eliminar {cli.nombre}')
        return redirect('ListaCliente')    
