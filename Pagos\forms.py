from django import forms
from .models import Gasto, PagoFacturaCredito


class GastoForm(forms.ModelForm):
   
    class Meta:
        model = Gasto
        fields = ['id_gasto','nombre','cantidad','precio_uni','total','fecha_pago']

        widgets = { 
            'id_gasto':forms.TextInput(attrs={'class': 'form-control','require':True,'readonly':True}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','requiere': True}),
            'cantidad': forms.TextInput(attrs={'class': 'form-control','require':True}),
            'precio_uni': forms.TextInput(attrs={'class': 'form-control','require':True}),
            'total': forms.TextInput(attrs={'class': 'form-control','require':True}), 
            'fecha_pago': forms.TextInput(attrs={'class': 'form-control','require':True}),
        }




TIPO = (('ABONO', 'ABONO'), ('TOTALIDAD', 'TOTALIDAD'))

class PagoFacturaCreditoForm(forms.ModelForm):
    class Meta:
        model = PagoFacturaCredito
        fields = ['tipo', 'abono']
        widgets = {
            'tipo': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info'}, choices=TIPO),
            'abono': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '0.00'}),
        }
