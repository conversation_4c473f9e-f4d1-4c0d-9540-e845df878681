import datetime
from decimal import Decimal
from django.contrib import messages  
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.db.models import Q
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.urls import reverse
from Creditos.forms import PagoForm
from Creditos.reportes import ComprobanteCredito
from .models import Venta, Pago
from django.shortcuts import get_object_or_404, redirect, render
from django.db import transaction


@login_required
def lista_pagos_pendientes(request):
    query = request.GET.get('q', '')
    # Excluir CreditoFel de los pagos pendientes normales
    ventas = Venta.objects.filter(estado=3).exclude(tipo='CreditoFel')

    if query:
        ventas = ventas.filter(
            Q(nit__icontains=query) |
            Q(nombre__icontains=query) |
            Q(direccion__icontains=query)
        )

    return render(request, 'lista_pagos_pendientes.html', {'ventas': ventas, 'query': query})


@login_required
def pago(request, f):
    form = PagoForm()
    venta = Venta.objects.get(factura=f)
    verfalta = Pago.objects.filter(factura=venta).aggregate(adeuda=Sum('abono'))

    if verfalta['adeuda'] is None:
        verfalta['adeuda'] = Decimal(0.00)

    queda = venta.total - verfalta['adeuda']
    nuevo_total = queda  

    if request.method == "POST":
        form = PagoForm(request.POST)
        if form.is_valid():
            nuevo_abono = form.cleaned_data['abono']
            descuento_porcentaje = form.cleaned_data.get('descuento_porcentaje', 0)
            descuento_cantidad = 0

            # Si se selecciona la opción 'TOTALIDAD' y se aplica un descuento
            if form.cleaned_data['tipo'] == 'TOTALIDAD' and descuento_porcentaje:
                descuento_cantidad = (queda * descuento_porcentaje) / 100
                nuevo_total = queda - descuento_cantidad
                nuevo_abono = nuevo_total  # Actualizar el abono al nuevo total con descuento

            if nuevo_total >= nuevo_abono:
                p = Pago()
                p.factura = venta
                p.tipo = form.cleaned_data['tipo']
                p.nit = venta.nit
                p.abono = nuevo_abono
                p.fecha = datetime.date.today()
                p.estado = 1
                p.usuario = request.user
                p.descuento_porcentaje = descuento_porcentaje
                p.descuento_cantidad = descuento_cantidad
                p.save()

                # Cambiar el estado de la factura si se ha pagado la totalidad o si se aplica un descuento
                if (queda <= nuevo_abono) or (form.cleaned_data['tipo'] == 'TOTALIDAD' and descuento_cantidad > 0):
                    venta.estado = 1  # Marcar la factura como pagada
                    venta.save()

                    # Redirigir a la vista del PDF en todos los casos
                    return redirect('pdf_creditoVenta', f)
                
                return redirect('ListaPagoPendiente')


            else:
                messages.error(request, f'El abono de Q{nuevo_abono} supera el saldo pendiente de Q{nuevo_total}')
                return redirect('NuevoPago', f)

    pagos_realizados = Pago.objects.filter(factura=venta)

    return render(request, 'pago.html', {
        'form': form,
        'venta': venta,
        'queda': queda,
        'nuevo_total': nuevo_total,
        'pagos_realizados': pagos_realizados
    })




def lista_pagos_pendientes(request):
    query = request.GET.get('q', '')
    # Excluir CreditoFel de los pagos pendientes normales
    ventas = Venta.objects.filter(estado=3).exclude(tipo='CreditoFel')

    if query:
        ventas = ventas.filter(
            Q(nit__icontains=query) |
            Q(nombre__icontains=query) |
            Q(direccion__icontains=query)
        )

    ventas_con_abonos = []
    for venta in ventas:
        abonos = Pago.objects.filter(factura=venta).aggregate(abono_total=Sum('abono'))
        if abonos['abono_total'] is None:
            abonos['abono_total'] = Decimal(0.00)
        
        queda = venta.total - abonos['abono_total']
        ventas_con_abonos.append({
            'venta': venta,
            'abono_total': abonos['abono_total'],
            'queda': queda
        })

    return render(request, 'lista_pagos_pendientes.html', {
        'ventas_con_abonos': ventas_con_abonos,
        'query': query,
    })



def pdf_credito(request, f):
    if not request.user.is_authenticated:
        return redirect('/')
    
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="reporte_credito_{f}.pdf"'
    reporte = ComprobanteCredito(f)
    response.write(reporte.run())
    return response




