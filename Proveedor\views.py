from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from Proveedor.models import Proveedor
from Proveedor.forms import ProveedorForm,UpdateProveedorForm
from datetime import datetime
from user.models import User
from django.contrib import messages



@login_required
def nuevo(request):
    form = ProveedorForm()

    if request.method == "POST":
        form = ProveedorForm(request.POST)
        if form.is_valid():
            try:
                p = Proveedor()
                p.nit = form.cleaned_data['nit']
                p.nombre = form.cleaned_data['nombre']
                p.direccion = form.cleaned_data['direccion']
                p.telefono = form.cleaned_data['telefono']
                p.estado = 1
                p.fecha = datetime.today()
                p.usuario = User.objects.get(id=request.user.id)
                p.save()
                messages.success(request,f'El Proveedor {p.nombre} Fue Ingresado Exitosamente!')
                return redirect('NuevoProveedor')
            except:
                messages.error(request,f'El Proveedor {p.nombre} NO Fue Ingresado!')
                return redirect('NuevoProveedor')



    return render(request,'Proveedor/nuevo.html',{'form':form})


@login_required
def actualizar(request,nit):
    
    if Proveedor.objects.filter(nit=nit).exists():
        prov = Proveedor.objects.get(nit=nit)
   
        if request.method == "GET":
            form = UpdateProveedorForm(instance=prov)
        else:
            form = UpdateProveedorForm(request.POST,instance=prov)

            if form.is_valid():
                try:
                    prov.fecha = datetime.today()
                    prov.usuario = User.objects.get(id=request.user.id)
                    form.save()
                    messages.success(request,f'Proveedor {prov.nombre} Se Actualizo Correctamente!') 
                    return redirect('ListaProveedor')
                except:
                    messages.error(request,f'Proveedor {prov.nombre} NO Se Actualizo Correctamente!') 
                    return redirect('ListaProveedor')

        return render(request,'Proveedor/actualizar.html',{'form':form})

    else:
        messages.error(request,f'Proveedor {nit} No Existe Verifica en Sistema!')
        return redirect('ListaProveedor')

        





@login_required
def lista(request):
    datos = Proveedor.objects.all().order_by('id')
    return render(request,'Proveedor/lista.html',{'prov':datos})


@login_required
def alta(request,nit):
    
    prov = Proveedor.objects.get(nit=nit)
    if Proveedor.objects.filter(nit=nit).exists():
        Proveedor.objects.filter(nit=nit).update(estado=1)
        messages.success(request,f'Proveedor {prov.nombre} Fue Dado de Alta')
        return redirect('ListaProveedor')
    else:
        messages.error(request,f'Proveedor {nit} No Existe Verifica en Sistema!')
        return redirect('ListaProveedor')



@login_required
def baja(request,nit):
    
    prov = Proveedor.objects.get(nit=nit)
    if Proveedor.objects.filter(nit=nit).exists():
        Proveedor.objects.filter(nit=nit).update(estado=2)
        messages.success(request,f'Proveedor {prov.nombre} Fue Dado de Baja')
        return redirect('ListaProveedor')
    else:
        messages.error(request,f'Proveedor {nit} No Existe Verifica en Sistema!')
        return redirect('ListaProveedor')