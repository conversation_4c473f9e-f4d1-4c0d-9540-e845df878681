from Venta import views
from django.urls import path
from .views import lista_ventas_pagadas, pdf_credito

urlpatterns = [
    path('nueva/',views.venta,name='Venta'),
    path('ventashoy/',views.ventashoy,name='ListaVentaHoy'),
    path('ventashoy2/',views.ventashoy2,name='ListaVentaHoy2'),
    path('todasventas/',views.todasventas,name='ListaVentaTodas'),
    path('todasventas2/',views.todasventas2,name='ListaVentaTodas2'),
    path('todasventascredito/',views.todasventascredito,name='ListaVentaTodasCredito'),
    path('detalle/<str:f>',views.detalle,name='Detalle'),
    path('descartar/<str:f>',views.descartar,name='Descartar'),
    path('anular/<str:f>',views.anularventa,name='Anular'),
    path('anularventa/<str:f>',views.anularventa2,name='Anular2'),
    path('comprobante/<str:f>',views.pdf,name='PDF'),

    path('cotizacion/<str:f>',views.detallecotizacion,name='DetalleCotizacion'),
    path('cotizaciones/', views.listacotizacion, name='lista_cotizacion'),
    path('cotizacion/anular/<str:token>/', views.anular_cotizacion, name='AnularCotizacion'),
    path('cotizacion/<str:f>',views.pdf,name='PDFCOTIZACION'), # esta es para editar
    path('venta/cotizacion/pdf/<str:f>/', views.pdfcotizacion, name='pdfcotizacion'), #para imprimir
    
    path('detalle_venta_cliente/<int:venta_id>/', views.detalle_venta_cliente, name='detalle_venta_cliente'),
    path('eliminar/<int:venta_id>/', views.eliminar_venta, name='eliminar_venta'),
    
    
    path('cobro/<str:f>',views.cobro,name='CobroVenta'),

    path('gasto/',views.gasto,name='Gasto'),
    path('listagasto/',views.listagasto,name='TodoGasto'),
    path('listagastohoy/',views.listagastohoy,name='TodoGastoHoy'),
    
    path('estadisticas/',views.estadisticas,name='Estadistica'),
    path('credito/pagos_lista/', lista_ventas_pagadas, name='lista_ventas_pagadas'),
    path('pagos/pdf/<str:f>/', pdf_credito, name='pdf_creditoVenta'),
    path('anulafel/<str:t>/', views.anularfel, name='AnulaFEL'),
    
    path('pasar-a-fel/<str:t>/', views.pasa_a_fel, name='PasarFEL'),

    # URLs para Créditos FEL
    path('creditos-fel/', views.lista_creditos_fel, name='lista_creditos_fel'),
    path('creditos-fel/pagados/', views.lista_creditos_fel_pagados, name='lista_creditos_fel_pagados'),
    path('creditos-fel/pagar/<str:token>/', views.pagar_credito_fel, name='pagar_credito_fel'),
    path('creditos-fel/detalle/<str:token>/', views.detalle_credito_fel, name='detalle_credito_fel'),

]
