{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Pagar Crédito FEL</h4>
                    <p class="card-description">Procesar pago completo del crédito FEL</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Información de la Venta</h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Número de Venta:</strong> {{ venta.factura }}
                        </div>
                        <div class="col-md-6">
                            <strong>Fecha de Venta:</strong> {{ venta.fecha|date:"d/m/Y" }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Cliente:</strong> {{ venta.nombre }}
                        </div>
                        <div class="col-md-6">
                            <strong>NIT:</strong> {{ venta.nit }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Dirección:</strong> {{ venta.direccion }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Total a Pagar:</strong> 
                            <span class="text-success font-weight-bold">Q.{{ venta.total }}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Estado:</strong> 
                            <span class="badge badge-warning">Pendiente de Pago</span>
                        </div>
                    </div>
                    
                    {% if venta.link %}
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Factura FEL:</strong>
                            <a href="{{ venta.link }}" target="_blank" class="btn btn-info btn-sm ml-2">
                                <i class="fa fa-file-pdf"></i> Ver Factura FEL
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Procesar Pago</h5>
                    
                    <form method="POST">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="monto_display">Monto a Pagar:</label>
                            <input type="text" id="monto_display" class="form-control" 
                                   value="Q.{{ venta.total }}" readonly>
                            <small class="form-text text-muted">
                                Solo se permite pago completo (no abonos)
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="observaciones">Observaciones (Opcional):</label>
                            <textarea name="observaciones" id="observaciones" 
                                      class="form-control" rows="3" 
                                      placeholder="Agregar observaciones sobre el pago..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <strong>Fecha de Pago:</strong> {{ "now"|date:"d/m/Y" }}
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg" 
                                    onclick="return confirm('¿Está seguro de procesar este pago? Esta acción no se puede deshacer.')">
                                <i class="fa fa-money"></i> Procesar Pago
                            </button>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="{% url 'lista_creditos_fel' %}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Volver a Lista
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Información adicional -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Información Importante</h5>
                    <div class="alert alert-info">
                        <ul class="mb-0">
                            <li>Este crédito FEL ya fue procesado fiscalmente y tiene su factura FEL generada</li>
                            <li>Al procesar el pago, el estado cambiará a "Pagado" y se registrará en la bitácora</li>
                            <li>Solo se permite el pago completo del monto total</li>
                            <li>Una vez procesado el pago, no se puede revertir</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
