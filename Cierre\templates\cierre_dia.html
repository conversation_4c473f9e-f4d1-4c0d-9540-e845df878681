{% extends 'BaseInicio/base.html' %}
{% load humanize %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-calculator"></i> Cierre del Día - {{ hoy }}</h3>
        </div>
        
        <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
                {% endfor %}
            {% endif %}

            {% if not cierre_hecho %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Revise cuidadosamente los totales antes de registrar el cierre.
                </div>
            {% endif %}

            <div class="row">
                <!-- Ingresos -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h4><i class="fas fa-money-bill-wave"></i> Ingresos</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Tipo</th>
                                        <th class="text-right">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Caja Chica</td>
                                        <td class="text-right">Q {{ total_caja_chica|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                    <tr>
                                        <td>Ventas al Contado</td>
                                        <td class="text-right">Q {{ total_ventas|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                    <tr>
                                        <td>Abonos a Créditos</td>
                                        <td class="text-right">Q {{ total_abonos|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                    <tr>
                                        <td>Pago Créditos FEL</td>
                                        <td class="text-right">Q {{ pagos_creditos_fel|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                </tbody>
                                <tfoot class="font-weight-bold">
                                    <tr>
                                        <td>Total Ingresos</td>
                                        <td class="text-right">Q {{ total_ingresos|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Egresos -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h4><i class="fas fa-sign-out-alt"></i> Egresos</h4>
                        </div>
                        <div class="card-body">
                            <table class="table table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Tipo</th>
                                        <th class="text-right">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Pagos/Gastos</td>
                                        <td class="text-right text-danger">Q {{ total_pagos|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                    <tr>
                                        <td>Pago a Facturas</td>
                                        <td class="text-right text-danger">Q {{ total_facturas_credito|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                </tbody>
                                <tfoot class="font-weight-bold">
                                    <tr>
                                        <td>Total Egresos</td>
                                        <td class="text-right text-danger">Q {{ total_salidas|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                    <tr class="{% if balance >= 0 %}bg-light text-success{% else %}bg-light text-danger{% endif %}">
                                        <td>Balance del Día</td>
                                        <td class="text-right font-weight-bold">Q {{ balance|default:0|floatformat:2|intcomma }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resumen adicional -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h4><i class="fas fa-file-invoice-dollar"></i> Resumen Adicional</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="alert alert-secondary">
                                <h5>Cotizaciones</h5>
                                <h3>Q {{ cotizaciones_dia|default:0|floatformat:2|intcomma }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="alert alert-warning">
                                <h5>Ventas a Crédito</h5>
                                <h3>Q {{ ventas_credito_neto|default:0|floatformat:2|intcomma }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="alert alert-info">
                                <h5>Créditos FEL</h5>
                                <h3>Q {{ creditos_fel_pendientes|default:0|floatformat:2|intcomma }}</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="alert alert-dark">
                                <h5>Facturas a Crédito</h5>
                                <h3>Q {{ facturas_credito_dia|default:0|floatformat:2|intcomma }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if cierre_hecho %}
                <div class="alert alert-success text-center">
                    <i class="fas fa-check-circle fa-2x"></i>
                    <h4>El cierre del día ya fue registrado</h4>
                </div>
            {% else %}
                <form method="post" class="text-center mt-3">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary btn-lg"
                            onclick="return confirm('¿Confirmar registro del cierre del día?')">
                        <i class="fas fa-save"></i> Registrar Cierre
                    </button>
                </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
