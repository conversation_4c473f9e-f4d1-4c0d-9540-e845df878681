{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Detalle Crédito FEL #{{ venta.factura }}</h4>
                    <p class="card-description">Información completa del crédito FEL</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de la venta -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Información de la Venta</h5>
                    
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Número de Venta:</strong></td>
                            <td>{{ venta.factura }}</td>
                        </tr>
                        <tr>
                            <td><strong><PERSON><PERSON> Venta:</strong></td>
                            <td>{{ venta.fecha|date:"d/m/Y" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Cliente:</strong></td>
                            <td>{{ venta.nombre }}</td>
                        </tr>
                        <tr>
                            <td><strong>NIT:</strong></td>
                            <td>{{ venta.nit }}</td>
                        </tr>
                        <tr>
                            <td><strong>Dirección:</strong></td>
                            <td>{{ venta.direccion }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total:</strong></td>
                            <td class="text-success font-weight-bold">Q.{{ venta.total }}</td>
                        </tr>
                        <tr>
                            <td><strong>Estado:</strong></td>
                            <td>
                                {% if venta.estado == 3 %}
                                    <span class="badge badge-warning">Pendiente de Pago</span>
                                {% elif venta.estado == 1 %}
                                    <span class="badge badge-success">Pagado</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Tipo:</strong></td>
                            <td><span class="badge badge-info">Credito FEL</span></td>
                        </tr>
                        <tr>
                            <td><strong>Usuario:</strong></td>
                            <td>{{ venta.usuario.username }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Información FEL</h5>
                    
                    <table class="table table-borderless">
                        {% if venta.serie %}
                        <tr>
                            <td><strong>Serie FEL:</strong></td>
                            <td>{{ venta.serie }}</td>
                        </tr>
                        {% endif %}
                        {% if venta.numero %}
                        <tr>
                            <td><strong>Número FEL:</strong></td>
                            <td>{{ venta.numero }}</td>
                        </tr>
                        {% endif %}
                        {% if venta.fecha_fel %}
                        <tr>
                            <td><strong>Fecha FEL:</strong></td>
                            <td>{{ venta.fecha_fel }}</td>
                        </tr>
                        {% endif %}
                        {% if venta.anula %}
                        <tr>
                            <td><strong>UUID FEL:</strong></td>
                            <td><small>{{ venta.anula }}</small></td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><strong>Factura FEL:</strong></td>
                            <td>
                                {% if venta.link %}
                                    <a href="{{ venta.link }}" target="_blank" class="btn btn-info btn-sm">
                                        <i class="fa fa-file-pdf"></i> Ver Factura
                                    </a>
                                {% else %}
                                    <span class="text-muted">No disponible</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Detalles de productos -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Productos de la Venta</h5>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Producto</th>
                                    <th>Cantidad</th>
                                    <th>Precio Unitario</th>
                                    <th>Descuento</th>
                                    <th>Subtotal</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for detalle in detalles %}
                                <tr>
                                    <td>
                                        {% if detalle.nombre_custom %}
                                            {{ detalle.nombre_custom }}
                                        {% else %}
                                            {{ detalle.id_inventario.nombre }}
                                        {% endif %}
                                    </td>
                                    <td>{{ detalle.cantidad }}</td>
                                    <td>Q.{{ detalle.precio_uni }}</td>
                                    <td>Q.{{ detalle.descuento }}</td>
                                    <td>Q.{{ detalle.subtotal }}</td>
                                    <td>Q.{{ detalle.total }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">No hay productos registrados</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Historial de pagos -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Historial de Pagos</h5>
                    
                    {% if pagos %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Fecha de Pago</th>
                                        <th>Monto Pagado</th>
                                        <th>Usuario</th>
                                        <th>Observaciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for pago in pagos %}
                                    <tr>
                                        <td>{{ pago.fecha_pago|date:"d/m/Y" }}</td>
                                        <td class="text-success">Q.{{ pago.monto_pagado }}</td>
                                        <td>{{ pago.usuario.username }}</td>
                                        <td>{{ pago.observaciones|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> Este crédito aún no ha sido pagado.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Acciones</h5>
                    
                    <div class="btn-group" role="group">
                        {% if venta.estado == 3 %}
                            <a href="{% url 'pagar_credito_fel' venta.token %}" class="btn btn-success">
                                <i class="fa fa-money"></i> Procesar Pago
                            </a>
                        {% endif %}
                        
                        {% if venta.link %}
                            <a href="{{ venta.link }}" target="_blank" class="btn btn-info">
                                <i class="fa fa-file-pdf"></i> Ver Factura FEL
                            </a>
                        {% endif %}
                        
                        {% if venta.estado == 3 %}
                            <a href="{% url 'lista_creditos_fel' %}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Volver a Pendientes
                            </a>
                        {% else %}
                            <a href="{% url 'lista_creditos_fel_pagados' %}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Volver a Pagados
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
