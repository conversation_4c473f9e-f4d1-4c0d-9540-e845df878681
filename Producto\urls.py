from Producto import views,viewsprodfactura
from django.urls import path
from .viewsprodfactura import FacturaDeleteView, FacturaDetailView, FacturaListView, lista


urlpatterns = [
    path('',views.nuevo,name="NuevoProducto"),
    path('listadoproductos/',views.listado,name="ListaProducto"),
    path('listadoproductos2/',views.listado2,name="ListaProducto2"),
    path('eliminarproducto/<int:id>',views.eliminar,name="DeleteProducto"),
    path('ingresofactura/',viewsprodfactura.nuevo,name='IngresoFactura'),
    path('listaingresos/',viewsprodfactura.lista,name='ListaIngresoFactura'),
    path('detalleingresofactura/<int:f>',viewsprodfactura.detalle,name='DetalleIngresoFactura'),
    path('bajaingresofactura/<int:f>',viewsprodfactura.darbaja,name='BajaIngresoFactura'),
    path('modificarproducto/<int:id>',views.actualizar,name="UpdateProducto"),
    path('modificarproducto/<int:id>',views.actualizar2,name="UpdateProducto2"),
    path('ventas-y-facturas/', lista, name='ventas_y_facturas'),
    path('producto/<int:producto_id>/bitacora/', views.bitacora_producto, name='bitacora_producto'),

    path('facturas/', FacturaListView.as_view(), name='factura_list'),
    path('factura/<int:pk>/delete/', FacturaDeleteView.as_view(), name='factura_delete'),
    path('factura/<int:pk>/', FacturaDetailView.as_view(), name='factura_detail'),
]
