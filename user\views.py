from django.shortcuts import render,redirect
from user.models import User
from user.forms import RegistroForm
from django.contrib import messages
from django.contrib.auth.hashers import make_password
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import update_session_auth_hash


def nuevousuario(request):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        form = RegistroForm()
        if request.method == "POST":
            form = RegistroForm(request.POST)
            if form.is_valid():
                try:
                    u = User()
                    u.username = form.cleaned_data["username"]
                    u.password = make_password(form.cleaned_data["password"])
                    u.first_name = form.cleaned_data["first_name"]
                    u.last_name = form.cleaned_data["last_name"]
                    u.email = form.cleaned_data["email"]
                    u.rol = form.cleaned_data["rol"]
                    u.is_staff = form.cleaned_data["is_staff"]
                    u.is_active = form.cleaned_data["is_active"]
                    u.is_superuser = form.cleaned_data["is_superuser"]
                    u.categoria = form.cleaned_data["categoria"]
                    u.save()
                    messages.success(request,'Registro de Usuario Exitoso')
                    return redirect('NuevoUser')
                except:
                    messages.error(request,'Registro de Usuario Fallido')
                    return redirect('NuevoUser')

            else:
                messages.error(request,"Formulario Corrupto")
                return redirect('NuevoUser')


        return render(request,"user/nuevousuario.html",{'form':form})




def nuevousuario2(request):
        form = RegistroForm()
        if request.method == "POST":
            form = RegistroForm(request.POST)
            if form.is_valid():
                try:
                    u = User()
                    u.username = form.cleaned_data["username"]
                    u.password = make_password(form.cleaned_data["password"])
                    u.first_name = form.cleaned_data["first_name"]
                    u.last_name = form.cleaned_data["last_name"]
                    u.email = form.cleaned_data["email"]
                    u.rol = form.cleaned_data["rol"]
                    u.is_staff = form.cleaned_data["is_staff"]
                    u.is_active = form.cleaned_data["is_active"]
                    u.is_superuser = form.cleaned_data["is_superuser"]
                    u.save()
                    messages.success(request,'Registro de Usuario Exitoso')
                    return redirect('Login')
                except:
                    messages.error(request,'Registro de Usuario Fallido')
                    return redirect('Login')

            else:
                messages.error(request,"Formulario Corrupto")
                return redirect('Login')


        return render(request,"user/registro.html",{'form':form})





def listausuario(request):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        usuarios = User.objects.all()
        return render(request,"user/todousuario.html",{'usuarios':usuarios})




@login_required
def updateusuario(request, id):
    # Obtener el usuario
    usuario = get_object_or_404(User, id=id)

    if not request.user.is_active or request.user.rol != 'admin':
        messages.error(request, 'No tienes permiso para realizar esta acción.')
        return redirect('/')

    if request.method == 'POST':
        # Diferenciar entre actualización de datos y cambio de contraseña
        if 'nuevo' in request.POST and 'confirmar' in request.POST:
            # Cambio de contraseña
            nueva_clave = request.POST.get('nuevo', '').strip()
            confirmar_clave = request.POST.get('confirmar', '').strip()

            if not nueva_clave or not confirmar_clave:
                messages.error(request, 'Los campos de contraseña no pueden estar vacíos.')
            elif nueva_clave != confirmar_clave:
                messages.error(request, 'Las contraseñas no coinciden.')
            else:
                try:
                    usuario.set_password(nueva_clave)
                    usuario.save()
                    update_session_auth_hash(request, usuario)  # Mantiene la sesión activa
                    messages.success(request, 'Contraseña modificada exitosamente.')
                    return redirect('ListaUser')  # Ajusta según tus rutas
                except Exception as e:
                    messages.error(request, f'Error al cambiar la contraseña: {str(e)}')

        else:
            # Actualización de datos del usuario
            form = RegistroForm(request.POST, request.FILES, instance=usuario)
            if form.is_valid():
                try:
                    form.save()
                    messages.success(request, 'Usuario modificado exitosamente.')
                    return redirect('ListaUser')  # Ajusta según tus rutas
                except Exception as e:
                    messages.error(request, f'Error al actualizar el usuario: {str(e)}')

    else:
        form = RegistroForm(instance=usuario)

    return render(request, "user/updateusuario.html", {
        'form': form,
        'usuario': usuario,
    })




@login_required
def updatepass(request,id):

    if request.method == 'POST':
        usuarios = User.objects.get(id=id)
        usuarios.set_password(request.POST['nuevo'])
        usuarios.save()
        messages.success(request, 'Usuario Modificado Exitosamente!.')
        return redirect('/')

    
    return render(request,"user/updatepass.html")


def deleteusuario(request,id):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        usuarios = User.objects.get(username=id)
        print(usuarios.username)
        if usuarios.username == request.user:
            messages.error(request, 'No Puedes Eliminar Tu Propio Usuario!.')
            return redirect('ListaUser')          
         
        else:
            if request.method == 'GET':
                try:
                    usuarios.delete() 
                    messages.success(request, 'Usuario Eliminado Exitosamente!.')
                    return redirect('ListaUser')#la redireccion es en name del path en la url  path('',views.compras,name="Compras"),
                except:
                    messages.error(request, 'Eliminacion de Usuario Fallido!.')
                    return redirect('ListaUser') # path('error/',views.error,name="Error"),
            else:
              pass           
  

