from decimal import Decimal
from django.db import models

from django.db import models

class Persona(models.Model):
    dpi = models.CharField(max_length=250, unique=True, blank=False, null=False)  # DPI único
    nombre = models.CharField(max_length=250, blank=False, null=False)
    apellido = models.CharField(max_length=250, blank=False, null=False)
    direccion = models.CharField(max_length=850, blank=False, null=False)
    telefono = models.CharField(max_length=9, blank=False, null=False)
    puesto = models.CharField(max_length=850, blank=False, null=False)
    salario_base = models.DecimalField(max_digits=12, decimal_places=2, blank=False, null=False, default=0.00)
    contacto_emergencia = models.CharField(max_length=250, blank=False, null=False)
    telefono_contacto = models.CharField(max_length=9, blank=False, null=False)
    fecha_inicio = models.CharField(max_length=250, blank=True, null=True, default='')
    fecha_baja = models.CharField(max_length=250, blank=True, null=True, default='')
    estado_civil = models.CharField(max_length=250, blank=False, null=False)
    estado = models.IntegerField(blank=False, null=False, default=1)
    fecha = models.CharField(max_length=250, blank=True, null=True, default='')

    class Meta:
        ordering = ["dpi"]

    def __str__(self):
        return self.dpi


from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Sum

class PlanillaBase(models.Model):
    NOMBRE_PLANILLA_CHOICES = [
        ('Oficina', 'Oficina'),
     
    ]
    
    nombre_planilla = models.CharField(max_length=50, choices=NOMBRE_PLANILLA_CHOICES, blank=False, null=False)
    mes = models.CharField(max_length=50, blank=False, null=False)
    ciclo = models.IntegerField(blank=True, null=True)
    estado = models.IntegerField(blank=False, null=False, default=1)
    fecha_creacion = models.DateField(auto_now_add=True)

    def __str__(self):
        return f"{self.nombre_planilla} - {self.mes} - Ciclo {self.ciclo}"

    def calcular_total_sueldos(self):
        # Calcula la suma del campo 'liquido' para todos los empleados en esta planilla
        total_sueldos = self.empleados.aggregate(Sum('liquido'))['liquido__sum'] or Decimal('0.00')
        return total_sueldos



class Planilla(models.Model):
    planilla_base = models.ForeignKey(PlanillaBase, on_delete=models.CASCADE, related_name="empleados")
    dpi = models.ForeignKey(Persona, on_delete=models.CASCADE, blank=False, null=False)
    salario_base = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    otras_deducciones = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    hora_extra = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)  # Nuevo campo para el valor por hora extra
    bonificacion = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)
    liquido = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00)

    class Meta:
        ordering = ["id"]

    def clean(self):
        if self.salario_base < 0:
            raise ValidationError("El salario base no puede ser negativo.")
        elif self.otras_deducciones < 0:
            raise ValidationError("Las otras deducciones no pueden ser negativas.")
        elif self.hora_extra < 0:
            raise ValidationError("Las horas extras no pueden ser negativas.")
        elif self.bonificacion < 0:
            raise ValidationError("La bonificación no puede ser negativa.")
        elif self.liquido < 0:
            raise ValidationError("El líquido no puede ser negativo.")


    def __str__(self):
        return str(self.id)



