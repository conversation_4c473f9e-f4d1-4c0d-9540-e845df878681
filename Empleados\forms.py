from django import forms
from .models import Persona,Planilla


ESTADO = (('1','Alta'),('0','Baja'))
ESTADO_CIVIL = (('Solter','Soltero'),('Casado','Casado'),('Unido','Unido'),('Di<PERSON>siado','Divorsiado'),('Viu<PERSON>','Viudo'))

class UpdatePersonaForm(forms.ModelForm):
   
    class Meta:
        model = Persona
        fields = ['estado','dpi','nombre','apellido','direccion','telefono','puesto','salario_base','contacto_emergencia','telefono_contacto','estado_civil']

        widgets = { 
            'dpi': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','autofocus': True,'require':True,'placeholder':'DPI'}),
            'nombre': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'Nombre de Trabajador'}),
            'apellido': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Apellido'}),
            'direccion': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Direccion'}),
            'telefono': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Telefono'}),
            'puesto': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Puesto'}),
            'salario_base': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Salario Base'}),
            'contacto_emergencia': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Contacto de Emergencia'}),
            'telefono_contacto': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'Telefono Contacto'}),
            'estado_civil':forms.Select(attrs={'style':'border: 1px solid black','class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Estado Civil','require':True},choices=ESTADO_CIVIL),
            'estado':forms.Select(attrs={'style':'border: 1px solid black','class': 'selectpicker form-control','data-style':'btn-outline-info','placeholder':'Estado','require':True},choices=ESTADO),
        }




from django import forms
from .models import Persona

class AgregarTrabajadorForm(forms.Form):
    buscar = forms.CharField(max_length=100, required=False, label="Buscar por DPI o Nombre")
    dpi = forms.ModelChoiceField(queryset=Persona.objects.none(), label="Seleccionar Trabajador")
    salario_base = forms.DecimalField(max_digits=12, decimal_places=2, required=True)
    otras_deducciones = forms.DecimalField(max_digits=12, decimal_places=2, required=True)
    horas_extras = forms.DecimalField(max_digits=12, decimal_places=2, required=True)
    bonificacion = forms.DecimalField(max_digits=12, decimal_places=2, required=True)


class PlanillaForm(forms.ModelForm):
   
    class Meta:
        model = Planilla
        fields = ['bonificacion','hora_extra','otras_deducciones','liquido']

        widgets = { 
            'bonificacion': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','autofocus': True,'require':True,'placeholder':'0.00','id':'boni'}),
            'hora_extra': forms.TextInput(attrs={'class': 'form-control','require':True,'autofocus': True,'placeholder':'0.00','id':'extra'}),
            'otras_deducciones': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'0.00','id':'otras'}),
            'liquido': forms.TextInput(attrs={'style':'border: 1px solid black','class': 'form-control','require':True,'placeholder':'0.00','id':'liquido','readonly':True}),
        }