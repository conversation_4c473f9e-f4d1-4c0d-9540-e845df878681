{% extends 'Base/base.html' %}
{% block title %}Estadisticas{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i>Estadisticas{%endblock %}
{% load static %}

{% block content %}

<div class="col-md-12 container-table">
    <div class="tile">
        <h3 class=" tile-title">Estadisticas</h3><br>
        <div class="row">

            <div class="col-md-12">
                {% if messages %}
                {% for message in messages %}
                <script>
                    Swal.fire({
                        "title": "Informacion Sistema",
                        "text": "{{message}}",
                        "icon": "{{message.tags}}"
                    })
                </script>
                {% endfor %}
                {% endif %}
            </div>

        </div><br>

        <div class="table-responsive">
            <table class="table table-bordered order-table">
                <thead style="font-size: 14px;">
                    <tr>
                        <th>Ventas Hechas Generales</th>
                        <th>Total Ventas Generales</th>
                        <th>Ventas Hechas Hoy</th>
                        <th>Total Ventas Hoy</th>
                        <th>Total Gastos Generales</th>
                        <th>Total Gastos Hoy</th>
                        <th>Total Ingresos Generales</th>
                        <th>Total Egresos Generales</th>
                        <th>Liquidez Total</th>
                        <th>Liquidez Hoy</th>
                    </tr>
                </thead>
                <tbody style="font-size: 14px;">
                    <tr>
                        <td>{{th}}</td>
                        <td>Q.{{tdv}}</td>
                        <td>{{tvh}}</td>
                        <td>Q.{{thh}}</td>
                        <td>Q.{{tdg}}</td>
                        <td>Q.{{tdgh}}</td>
                        <td>Q.{{i}}</td>
                        <td>Q.{{e}}</td>
                        <td>Q.{{l1}}</td>
                        <td>Q.{{l2}}</td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
</div>



<script type="text/javascript">
        (function (document) {
            'use strict';

            var LightTableFilter = (function (Arr) {

                var _input;

                function _onInputEvent(e) {
                    _input = e.target;
                    var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                    Arr.forEach.call(tables, function (table) {
                        Arr.forEach.call(table.tBodies, function (tbody) {
                            Arr.forEach.call(tbody.rows, _filter);
                        });
                    });
                }

                function _filter(row) {
                    var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
                    row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
                }

                return {
                    init: function () {
                        var inputs = document.getElementsByClassName('light-table-filter');
                        Arr.forEach.call(inputs, function (input) {
                            input.oninput = _onInputEvent;
                        });
                    }
                };
            })(Array.prototype);

            document.addEventListener('readystatechange', function () {
                if (document.readyState === 'complete') {
                    LightTableFilter.init();
                }
            });

        })(document);
</script>

{% endblock %}