{% extends 'BaseInicio/base.html' %}
{% block title %}Bitácora de {{ producto.nombre }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between">
            <h4 class="mb-0">
                <i class='bx bx-history'></i> Bitácora: {{ producto.nombre }}
                <small>
                    (Código: {{ producto.codigo }}, Stock actual: {{ producto.stock }})<br>
                    <strong>Proveedor:</strong>
                    {% if proveedor %}
                        {{ proveedor.nombre }}
                    {% else %}
                        No disponible
                    {% endif %}
                </small>
            </h4>

            <div class="mt-2">
                <span class="badge bg-success me-2">
                    Total comprado: {{ resumen.total_comprado }}
                </span>
                <span class="badge bg-danger">
                    Total vendido: {{ resumen.total_vendido }}
                </span>
            </div>

            <a href="{% url 'ListaProducto' %}" class="btn btn-sm btn-secondary">
                <i class='bx bx-arrow-back'></i> Volver
            </a>
        </div>
        <div class="card-body">
            <form method="get" class="row g-2 mb-3">
    <div class="col-md-3">
        <label for="fecha_inicio" class="form-label">Desde:</label>
        <input type="date" id="fecha_inicio" name="fecha_inicio" class="form-control"
               value="{{ fecha_inicio }}">
    </div>
    <div class="col-md-3">
        <label for="fecha_fin" class="form-label">Hasta:</label>
        <input type="date" id="fecha_fin" name="fecha_fin" class="form-control"
               value="{{ fecha_fin }}">
    </div>
    <div class="col-md-3 align-self-end">
        <button type="submit" class="btn btn-primary w-100">
            <i class='bx bx-filter'></i> Filtrar
        </button>
    </div>
</form>

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th>Estado</th>
                            <th>Fecha</th>
                            <th>Tipo</th>
                            <th>Documento</th>
                            <th>Proveedor</th>
                            <th>Ingreso</th>
                            <th>Salida</th>
                            <th>P. Unitario</th>
                            <th>Total</th>
                            <th>Stock</th>
                            <th>Usuario</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mov in movimientos %}
                        <tr>
                            <td>
                                {% if mov.tipo == 'SALIDA' %}
                                    {% if mov.anulada %}
                                        <span class="badge bg-warning text-dark">Anulada</span>
                                    {% else %}
                                        <span class="badge bg-success">Activa</span>
                                    {% endif %}
                                {% elif mov.tipo == 'ENTRADA' %}
                                    <span class="badge bg-primary">{{ mov.estado_pago }}</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>

                            <td>{{ mov.fecha|date:"d/m/Y" }}</td>

                            <td>
                                <span class="badge 
                                    {% if mov.tipo == 'ENTRADA' %}bg-success
                                    {% elif mov.tipo == 'SALIDA' %}bg-danger
                                    {% else %}bg-info{% endif %}">
                                    {{ mov.tipo }}
                                </span>
                            </td>
                            <td>{{ mov.documento }}</td>
                            <td>{{ mov.proveedor_nombre }}</td>
                            <td class="text-end">
                                {% if mov.tipo == 'ENTRADA' %}{{ mov.cantidad }}{% else %}-{% endif %}
                            </td>
                            <td class="text-end">
                                {% if mov.tipo == 'SALIDA' %}{{ mov.salida }}{% else %}-{% endif %}
                            </td>
                            <td class="text-end">Q {{ mov.precio_unitario|floatformat:2 }}</td>
                            <td class="text-end">Q {{ mov.total|floatformat:2 }}</td>
                            <td class="text-end">{{ mov.stock_actual }}</td>
                            <td>{{ mov.usuario }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="11" class="text-center">No hay movimientos registrados.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
