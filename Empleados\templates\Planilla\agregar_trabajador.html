{% extends 'BaseInicio/base.html' %}

{% block content %}
<br></br>
<div class="container" style="background-color: #f8f9fa; margin-top: 5px; max-width: 90%;">
    <a href="{% url 'Listado' %}" class="btn btn-warning">Regresar</a>
    <br></br>
    <!-- Formulario de búsqueda -->
    <form action="{% url 'agregar_trabajador' planilla.id %}" method="POST" enctype="multipart/form-data">{% csrf_token %}
        
        <div class="col-md-6">
            <label class="form-label">Buscar Por</label>
            <select name="tipo" class="form-control" required>
                <option value="DPI">DPI</option>
                <option value="Nombre">Nombre y Apellido</option>
            </select>
        </div>
        <div class="col-md-6">
            <label class="form-label">Buscar Trabajador por DPI o Nombre</label>
            <input type="text" class="form-control" name="buscar" placeholder="Ingresa DPI o Nombre" required>
            <button type="submit" class="btn btn-primary mt-2" name="buscar_empleado">Buscar</button>
        </div>
    </form>


    <!-- Formulario para agregar trabajador a la planilla -->
    <form action="{% url 'agregar_trabajador' planilla.id %}" method="POST" enctype="multipart/form-data">{% csrf_token %}
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-2 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-2 pb-2">
                        <h5 class="mb-0">Agregar Trabajador a Planilla: {{ planilla.nombre_planilla }}</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">DPI</label>
                            <input type="text" class="form-control" name="dpi" 
                                   value="{{ trabajador.dpi }}" 
                                   readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Trabajador Seleccionado</label>
                            <input type="text" class="form-control" name="trabajador_nombre" 
                                   value="{{ trabajador.nombre }} {{ trabajador.apellido }}" 
                                   readonly>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Salario Base</label>
                            <input type="number" class="form-control" name="salario_base" step="0.01" 
                                   value="{{ trabajador.salario_base }}" readonly id="base">
                        </div>
                    </div>
                    <br>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Bonificacion</label>
                            {{form.bonificacion}}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Horas Extras</label>
                            {{form.hora_extra}}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Otras Deducciones</label>
                            {{form.otras_deducciones}}
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Sueldo Líquido</label>
                            {{form.liquido}}
                        </div>
                    </div>
                    <br>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success" name="agregar">Agregar Trabajador</button>
                            <a href="{% url 'ListadoPlanilla' %}" class="btn btn-danger">Terminar</a>

                        </div>
                    </div>
                    <br>
                </div>
            </div>
        </div>
    </form>

    <!-- Bloque para mostrar mensajes -->
    <div class="col-md-12">
        {% if messages %}
            <div class="alert alert-dismissible fade show" role="alert">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <h2>Trabajadores Agregados</h2>
    <div class="table-responsive">
    <table class="table table-bordered table-hover table-sm">
        <thead>
            <tr>
                <td>Planilla</td>
                <td>Mes</td>
                <td>Empleado</td>
                <td>Liquido</td>
            </tr>
            <tbody>
                {% for p in pla %}
                <tr>
                    <td>{{p.planilla_base}}</td>
                    <td>{{p.planilla_base.mes}} {{p.planilla_base.ciclo}}</td>
                    <td>{{p.dpi.nombre}} {{p.dpi.apellido}}</td>
                    <td>Q.{{p.liquido}}</td>
                </tr>
                {% empty %}
                <caption>SIN EMPLEADOS</caption>
                {% endfor %}
            </tbody>
        </thead>
    </table>
    </div>
    
</div>


<script>
    let precio1 = document.getElementById("boni")
    let precio2 = document.getElementById("extra")
    let precio3 = document.getElementById("otras")
    let precio4 = document.getElementById("liquido")
    let precio5 = document.getElementById("base")
    
    precio3.addEventListener("change", () => {
        precio4.value = parseFloat(precio5.value) + (parseFloat(precio1.value) + (parseFloat(precio2.value)*20) - parseFloat(precio3.value))
    })
    
  </script>

{% endblock %}
