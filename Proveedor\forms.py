from django import forms
from Proveedor.models import Proveedor

class ProveedorForm(forms.ModelForm):

    class Meta:
        model = Proveedor
        fields = ['nit','nombre','direccion','telefono']

        widgets = {
            'nit':forms.TextInput(attrs={'class':'form-control','placeholder':'Nit de Proveedor','require':True,'autofocus':True,'style':'border: 1px solid; color:black;'}),
            'nombre':forms.TextInput(attrs={'class':'form-control','placeholder':'Nombre de Proveedor','require':True,'style':'border: 1px solid; color:black;'}),
            'direccion':forms.TextInput(attrs={'class':'form-control','placeholder':'Direccion de Proveedor','require':True,'style':'border: 1px solid; color:black;'}),
            'telefono':forms.TextInput(attrs={'class':'form-control','placeholder':'Telefono 00000000','require':True,'style':'border: 1px solid; color:black;'}),
        }


class UpdateProveedorForm(forms.ModelForm):

    class Meta:
        model = Proveedor
        fields = ['nit','nombre','direccion','telefono']

        widgets = {
            'nit':forms.TextInput(attrs={'class':'form-control','placeholder':'Nit de Proveedor','require':True,'autofocus':True,'style':'border: 1px solid; color:black;'}),
            'nombre':forms.TextInput(attrs={'class':'form-control','placeholder':'Nombre de Proveedor','require':True,'style':'border: 1px solid; color:black;'}),
            'direccion':forms.TextInput(attrs={'class':'form-control','placeholder':'Direccion de Proveedor','require':True,'style':'border: 1px solid; color:black;'}),
            'telefono':forms.TextInput(attrs={'class':'form-control','placeholder':'Telefono de Proveedor','require':True,'style':'border: 1px solid; color:black;'}),
        }
