{% extends 'BaseInicio/base.html' %}

{% block content %}
<br>
<div class="container" style="background-color: #f8f9fa; margin-top: 5px; max-width: 90%;">
    <br></br>
    <a href="{% url 'Listado' %}" class="btn btn-warning">Regresar</a>
    <a href="{% url 'crear_planilla' %}" class="btn btn-danger">Agregar Planilla</a>
    <br></br>
    <h2>Listado de Planillas</h2>
    
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th scope="col">Nombre de la Planilla</th>
                    <th scope="col">Mes</th>
                    <th scope="col">Ciclo</th>
                    <th scope="col">Estado</th>
                    <th scope="col">Fecha de Creación</th>
                    <th scope="col">Total de Sueldos</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                {% for item in planillas_con_totales %}
                    <tr>
                        <td>{{ item.planilla.nombre_planilla }}</td>
                        <td>{{ item.planilla.mes }}</td>
                        <td>{{ item.planilla.ciclo }}</td>
                        <td>{{ item.planilla.estado }}</td>
                        <td>{{ item.planilla.fecha_creacion }}</td>
                        <td>{{ item.total_sueldos }}</td>
                        <td>
                            <a href="{% url 'EliminarPlanilla' item.planilla.id %}">Eliminar</a>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="7">SIN REGISTRO DE PLANILLAS</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
