{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h3>Lista de Registros de Caja Chica</h3>
            <a href="{% url 'caja_chica_nuevo' %}" class="btn btn-success">Nuevo Registro</a>
        </div>
        <div class="card-body">
            <!-- Formulario de búsqueda -->
            <form method="get" class="mb-4">
                <div class="row">
                    <div class="col-md-4">
                        <input type="date" name="fecha" class="form-control" value="{{ query }}" placeholder="Buscar por fecha">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">Buscar</button>
                    </div>
                </div>
            </form>

            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Canti<PERSON></th>
                        <th>Fecha</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for registro in registros %}
                        <tr>
                            <td>{{ registro.id }}</td>
                            <td>{{ registro.cantidad }}</td>
                            <td>{{ registro.fecha_creacion }}</td>
                            <td>
                                <a href="{% url 'caja_chica_editar' registro.id %}" class="btn btn-sm btn-warning">Editar</a>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="4">No hay registros de caja chica.</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
