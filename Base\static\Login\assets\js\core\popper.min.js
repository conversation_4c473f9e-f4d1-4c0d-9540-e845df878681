/**
 * @popperjs/core v2.11.7 - MIT License
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Popper={})}(this,(function(e){"use strict";function t(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function n(e){return e instanceof t(e).Element||e instanceof Element}function r(e){return e instanceof t(e).HTMLElement||e instanceof HTMLElement}function o(e){return"undefined"!=typeof ShadowRoot&&(e instanceof t(e).ShadowRoot||e instanceof ShadowRoot)}var i=Math.max,a=Math.min,s=Math.round;function f(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function c(){return!/^((?!chrome|android).)*safari/i.test(f())}function p(e,o,i){void 0===o&&(o=!1),void 0===i&&(i=!1);var a=e.getBoundingClientRect(),f=1,p=1;o&&r(e)&&(f=e.offsetWidth>0&&s(a.width)/e.offsetWidth||1,p=e.offsetHeight>0&&s(a.height)/e.offsetHeight||1);var l=(n(e)?t(e):window).visualViewport,u=!c()&&i,d=(a.left+(u&&l?l.offsetLeft:0))/f,m=(a.top+(u&&l?l.offsetTop:0))/p,h=a.width/f,v=a.height/p;return{width:h,height:v,top:m,right:d+h,bottom:m+v,left:d,x:d,y:m}}function l(e){var n=t(e);return{scrollLeft:n.pageXOffset,scrollTop:n.pageYOffset}}function u(e){return e?(e.nodeName||"").toLowerCase():null}function d(e){return((n(e)?e.ownerDocument:e.document)||window.document).documentElement}function m(e){return p(d(e)).left+l(e).scrollLeft}function h(e){return t(e).getComputedStyle(e)}function v(e){var t=h(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function y(e,n,o){void 0===o&&(o=!1);var i,a,f=r(n),c=r(n)&&function(e){var t=e.getBoundingClientRect(),n=s(t.width)/e.offsetWidth||1,r=s(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(n),h=d(n),y=p(e,c,o),g={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(f||!f&&!o)&&(("body"!==u(n)||v(h))&&(g=(i=n)!==t(i)&&r(i)?{scrollLeft:(a=i).scrollLeft,scrollTop:a.scrollTop}:l(i)),r(n)?((b=p(n,!0)).x+=n.clientLeft,b.y+=n.clientTop):h&&(b.x=m(h))),{x:y.left+g.scrollLeft-b.x,y:y.top+g.scrollTop-b.y,width:y.width,height:y.height}}function g(e){var t=p(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function b(e){return"html"===u(e)?e:e.assignedSlot||e.parentNode||(o(e)?e.host:null)||d(e)}function w(e){return["html","body","#document"].indexOf(u(e))>=0?e.ownerDocument.body:r(e)&&v(e)?e:w(b(e))}function x(e,n){var r;void 0===n&&(n=[]);var o=w(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=t(o),s=i?[a].concat(a.visualViewport||[],v(o)?o:[]):o,f=n.concat(s);return i?f:f.concat(x(b(s)))}function O(e){return["table","td","th"].indexOf(u(e))>=0}function j(e){return r(e)&&"fixed"!==h(e).position?e.offsetParent:null}function E(e){for(var n=t(e),i=j(e);i&&O(i)&&"static"===h(i).position;)i=j(i);return i&&("html"===u(i)||"body"===u(i)&&"static"===h(i).position)?n:i||function(e){var t=/firefox/i.test(f());if(/Trident/i.test(f())&&r(e)&&"fixed"===h(e).position)return null;var n=b(e);for(o(n)&&(n=n.host);r(n)&&["html","body"].indexOf(u(n))<0;){var i=h(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||n}var S="top",P="bottom",A="right",k="left",D="auto",L=[S,P,A,k],M="start",T="end",q="clippingParents",W="viewport",H="popper",B="reference",R=L.reduce((function(e,t){return e.concat([t+"-"+M,t+"-"+T])}),[]),C=[].concat(L,[D]).reduce((function(e,t){return e.concat([t,t+"-"+M,t+"-"+T])}),[]),V=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function I(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}function N(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return[].concat(n).reduce((function(e,t){return e.replace(/%s/,t)}),e)}var F='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',U='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',_=["name","enabled","phase","fn","effect","requires","options"];function z(e){return e.split("-")[0]}function G(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&o(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function X(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Y(e,r,o){return r===W?X(function(e,n){var r=t(e),o=d(e),i=r.visualViewport,a=o.clientWidth,s=o.clientHeight,f=0,p=0;if(i){a=i.width,s=i.height;var l=c();(l||!l&&"fixed"===n)&&(f=i.offsetLeft,p=i.offsetTop)}return{width:a,height:s,x:f+m(e),y:p}}(e,o)):n(r)?function(e,t){var n=p(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(r,o):X(function(e){var t,n=d(e),r=l(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=i(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=i(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),f=-r.scrollLeft+m(e),c=-r.scrollTop;return"rtl"===h(o||n).direction&&(f+=i(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:s,x:f,y:c}}(d(e)))}function J(e,t,o,s){var f="clippingParents"===t?function(e){var t=x(b(e)),o=["absolute","fixed"].indexOf(h(e).position)>=0&&r(e)?E(e):e;return n(o)?t.filter((function(e){return n(e)&&G(e,o)&&"body"!==u(e)})):[]}(e):[].concat(t),c=[].concat(f,[o]),p=c[0],l=c.reduce((function(t,n){var r=Y(e,n,s);return t.top=i(r.top,t.top),t.right=a(r.right,t.right),t.bottom=a(r.bottom,t.bottom),t.left=i(r.left,t.left),t}),Y(e,p,s));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function K(e){return e.split("-")[1]}function Q(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Z(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?z(o):null,a=o?K(o):null,s=n.x+n.width/2-r.width/2,f=n.y+n.height/2-r.height/2;switch(i){case S:t={x:s,y:n.y-r.height};break;case P:t={x:s,y:n.y+n.height};break;case A:t={x:n.x+n.width,y:f};break;case k:t={x:n.x-r.width,y:f};break;default:t={x:n.x,y:n.y}}var c=i?Q(i):null;if(null!=c){var p="y"===c?"height":"width";switch(a){case M:t[c]=t[c]-(n[p]/2-r[p]/2);break;case T:t[c]=t[c]+(n[p]/2-r[p]/2)}}return t}function $(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function ee(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function te(e,t){void 0===t&&(t={});var r=t,o=r.placement,i=void 0===o?e.placement:o,a=r.strategy,s=void 0===a?e.strategy:a,f=r.boundary,c=void 0===f?q:f,l=r.rootBoundary,u=void 0===l?W:l,m=r.elementContext,h=void 0===m?H:m,v=r.altBoundary,y=void 0!==v&&v,g=r.padding,b=void 0===g?0:g,w=$("number"!=typeof b?b:ee(b,L)),x=h===H?B:H,O=e.rects.popper,j=e.elements[y?x:h],E=J(n(j)?j:j.contextElement||d(e.elements.popper),c,u,s),k=p(e.elements.reference),D=Z({reference:k,element:O,strategy:"absolute",placement:i}),M=X(Object.assign({},O,D)),T=h===H?M:k,R={top:E.top-T.top+w.top,bottom:T.bottom-E.bottom+w.bottom,left:E.left-T.left+w.left,right:T.right-E.right+w.right},C=e.modifiersData.offset;if(h===H&&C){var V=C[i];Object.keys(R).forEach((function(e){var t=[A,P].indexOf(e)>=0?1:-1,n=[S,P].indexOf(e)>=0?"y":"x";R[e]+=V[n]*t}))}return R}var ne="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",re={placement:"bottom",modifiers:[],strategy:"absolute"};function oe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function ie(e){void 0===e&&(e={});var t=e,r=t.defaultModifiers,o=void 0===r?[]:r,i=t.defaultOptions,a=void 0===i?re:i;return function(e,t,r){void 0===r&&(r=a);var i,s,f={placement:"bottom",orderedModifiers:[],options:Object.assign({},re,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],p=!1,l={state:f,setOptions:function(r){var i="function"==typeof r?r(f.options):r;u(),f.options=Object.assign({},a,f.options,i),f.scrollParents={reference:n(e)?x(e):e.contextElement?x(e.contextElement):[],popper:x(t)};var s=function(e){var t=I(e);return V.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(o,f.options.modifiers)));(f.orderedModifiers=s.filter((function(e){return e.enabled})),function(e){e.forEach((function(t){[].concat(Object.keys(t),_).filter((function(e,t,n){return n.indexOf(e)===t})).forEach((function(n){switch(n){case"name":"string"!=typeof t.name&&console.error(N(F,String(t.name),'"name"','"string"','"'+String(t.name)+'"'));break;case"enabled":"boolean"!=typeof t.enabled&&console.error(N(F,t.name,'"enabled"','"boolean"','"'+String(t.enabled)+'"'));break;case"phase":V.indexOf(t.phase)<0&&console.error(N(F,t.name,'"phase"',"either "+V.join(", "),'"'+String(t.phase)+'"'));break;case"fn":"function"!=typeof t.fn&&console.error(N(F,t.name,'"fn"','"function"','"'+String(t.fn)+'"'));break;case"effect":null!=t.effect&&"function"!=typeof t.effect&&console.error(N(F,t.name,'"effect"','"function"','"'+String(t.fn)+'"'));break;case"requires":null==t.requires||Array.isArray(t.requires)||console.error(N(F,t.name,'"requires"','"array"','"'+String(t.requires)+'"'));break;case"requiresIfExists":Array.isArray(t.requiresIfExists)||console.error(N(F,t.name,'"requiresIfExists"','"array"','"'+String(t.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+t.name+'" modifier, valid properties are '+_.map((function(e){return'"'+e+'"'})).join(", ")+'; but "'+n+'" was provided.')}t.requires&&t.requires.forEach((function(n){null==e.find((function(e){return e.name===n}))&&console.error(N(U,String(t.name),n,n))}))}))}))}((p=[].concat(s,f.options.modifiers),d=function(e){return e.name},m=new Set,p.filter((function(e){var t=d(e);if(!m.has(t))return m.add(t),!0})))),z(f.options.placement)===D)&&(f.orderedModifiers.find((function(e){return"flip"===e.name}))||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" ")));var p,d,m,v=h(t);return[v.marginTop,v.marginRight,v.marginBottom,v.marginLeft].some((function(e){return parseFloat(e)}))&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" ")),f.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var i=o({state:f,name:t,instance:l,options:r}),a=function(){};c.push(i||a)}})),l.update()},forceUpdate:function(){if(!p){var e=f.elements,t=e.reference,n=e.popper;if(oe(t,n)){f.rects={reference:y(t,E(n),"fixed"===f.options.strategy),popper:g(n)},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach((function(e){return f.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0,o=0;o<f.orderedModifiers.length;o++){if((r+=1)>100){console.error("Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.");break}if(!0!==f.reset){var i=f.orderedModifiers[o],a=i.fn,s=i.options,c=void 0===s?{}:s,u=i.name;"function"==typeof a&&(f=a({state:f,options:c,name:u,instance:l})||f)}else f.reset=!1,o=-1}}else console.error(ne)}},update:(i=function(){return new Promise((function(e){l.forceUpdate(),e(f)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(i())}))}))),s}),destroy:function(){u(),p=!0}};if(!oe(e,t))return console.error(ne),l;function u(){c.forEach((function(e){return e()})),c=[]}return l.setOptions(r).then((function(e){!p&&r.onFirstUpdate&&r.onFirstUpdate(e)})),l}}var ae={passive:!0};var se={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var n=e.state,r=e.instance,o=e.options,i=o.scroll,a=void 0===i||i,s=o.resize,f=void 0===s||s,c=t(n.elements.popper),p=[].concat(n.scrollParents.reference,n.scrollParents.popper);return a&&p.forEach((function(e){e.addEventListener("scroll",r.update,ae)})),f&&c.addEventListener("resize",r.update,ae),function(){a&&p.forEach((function(e){e.removeEventListener("scroll",r.update,ae)})),f&&c.removeEventListener("resize",r.update,ae)}},data:{}};var fe={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Z({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},ce={top:"auto",right:"auto",bottom:"auto",left:"auto"};function pe(e){var n,r=e.popper,o=e.popperRect,i=e.placement,a=e.variation,f=e.offsets,c=e.position,p=e.gpuAcceleration,l=e.adaptive,u=e.roundOffsets,m=e.isFixed,v=f.x,y=void 0===v?0:v,g=f.y,b=void 0===g?0:g,w="function"==typeof u?u({x:y,y:b}):{x:y,y:b};y=w.x,b=w.y;var x=f.hasOwnProperty("x"),O=f.hasOwnProperty("y"),j=k,D=S,L=window;if(l){var M=E(r),q="clientHeight",W="clientWidth";if(M===t(r)&&"static"!==h(M=d(r)).position&&"absolute"===c&&(q="scrollHeight",W="scrollWidth"),i===S||(i===k||i===A)&&a===T)D=P,b-=(m&&M===L&&L.visualViewport?L.visualViewport.height:M[q])-o.height,b*=p?1:-1;if(i===k||(i===S||i===P)&&a===T)j=A,y-=(m&&M===L&&L.visualViewport?L.visualViewport.width:M[W])-o.width,y*=p?1:-1}var H,B=Object.assign({position:c},l&&ce),R=!0===u?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:s(n*o)/o||0,y:s(r*o)/o||0}}({x:y,y:b},t(r)):{x:y,y:b};return y=R.x,b=R.y,p?Object.assign({},B,((H={})[D]=O?"0":"",H[j]=x?"0":"",H.transform=(L.devicePixelRatio||1)<=1?"translate("+y+"px, "+b+"px)":"translate3d("+y+"px, "+b+"px, 0)",H)):Object.assign({},B,((n={})[D]=O?b+"px":"",n[j]=x?y+"px":"",n.transform="",n))}var le={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,f=void 0===s||s,c=h(t.elements.popper).transitionProperty||"";a&&["transform","top","right","bottom","left"].some((function(e){return c.indexOf(e)>=0}))&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',"\n\n",'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.","\n\n","We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "));var p={placement:z(t.placement),variation:K(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,pe(Object.assign({},p,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,pe(Object.assign({},p,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ue={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},o=t.attributes[e]||{},i=t.elements[e];r(i)&&u(i)&&(Object.assign(i.style,n),Object.keys(o).forEach((function(e){var t=o[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var o=t.elements[e],i=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});r(o)&&u(o)&&(Object.assign(o.style,a),Object.keys(i).forEach((function(e){o.removeAttribute(e)})))}))}},requires:["computeStyles"]};var de={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=C.reduce((function(e,n){return e[n]=function(e,t,n){var r=z(e),o=[k,S].indexOf(r)>=0?-1:1,i="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[k,A].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}(n,t.rects,i),e}),{}),s=a[t.placement],f=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}},me={left:"right",right:"left",bottom:"top",top:"bottom"};function he(e){return e.replace(/left|right|bottom|top/g,(function(e){return me[e]}))}var ve={start:"end",end:"start"};function ye(e){return e.replace(/start|end/g,(function(e){return ve[e]}))}function ge(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,f=n.allowedAutoPlacements,c=void 0===f?C:f,p=K(r),l=p?s?R:R.filter((function(e){return K(e)===p})):L,u=l.filter((function(e){return c.indexOf(e)>=0}));0===u.length&&(u=l,console.error(["Popper: The `allowedAutoPlacements` option did not allow any","placements. Ensure the `placement` option matches the variation","of the allowed placements.",'For example, "auto" cannot be used to allow "bottom-start".','Use "auto-start" instead.'].join(" ")));var d=u.reduce((function(t,n){return t[n]=te(e,{placement:n,boundary:o,rootBoundary:i,padding:a})[z(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}var be={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,f=n.fallbackPlacements,c=n.padding,p=n.boundary,l=n.rootBoundary,u=n.altBoundary,d=n.flipVariations,m=void 0===d||d,h=n.allowedAutoPlacements,v=t.options.placement,y=z(v),g=f||(y===v||!m?[he(v)]:function(e){if(z(e)===D)return[];var t=he(e);return[ye(e),t,ye(t)]}(v)),b=[v].concat(g).reduce((function(e,n){return e.concat(z(n)===D?ge(t,{placement:n,boundary:p,rootBoundary:l,padding:c,flipVariations:m,allowedAutoPlacements:h}):n)}),[]),w=t.rects.reference,x=t.rects.popper,O=new Map,j=!0,E=b[0],L=0;L<b.length;L++){var T=b[L],q=z(T),W=K(T)===M,H=[S,P].indexOf(q)>=0,B=H?"width":"height",R=te(t,{placement:T,boundary:p,rootBoundary:l,altBoundary:u,padding:c}),C=H?W?A:k:W?P:S;w[B]>x[B]&&(C=he(C));var V=he(C),I=[];if(i&&I.push(R[q]<=0),s&&I.push(R[C]<=0,R[V]<=0),I.every((function(e){return e}))){E=T,j=!1;break}O.set(T,I)}if(j)for(var N=function(e){var t=b.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return E=t,"break"},F=m?3:1;F>0;F--){if("break"===N(F))break}t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function we(e,t,n){return i(e,a(t,n))}var xe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,s=void 0===o||o,f=n.altAxis,c=void 0!==f&&f,p=n.boundary,l=n.rootBoundary,u=n.altBoundary,d=n.padding,m=n.tether,h=void 0===m||m,v=n.tetherOffset,y=void 0===v?0:v,b=te(t,{boundary:p,rootBoundary:l,padding:d,altBoundary:u}),w=z(t.placement),x=K(t.placement),O=!x,j=Q(w),D="x"===j?"y":"x",L=t.modifiersData.popperOffsets,T=t.rects.reference,q=t.rects.popper,W="function"==typeof y?y(Object.assign({},t.rects,{placement:t.placement})):y,H="number"==typeof W?{mainAxis:W,altAxis:W}:Object.assign({mainAxis:0,altAxis:0},W),B=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(L){if(s){var C,V="y"===j?S:k,I="y"===j?P:A,N="y"===j?"height":"width",F=L[j],U=F+b[V],_=F-b[I],G=h?-q[N]/2:0,X=x===M?T[N]:q[N],Y=x===M?-q[N]:-T[N],J=t.elements.arrow,Z=h&&J?g(J):{width:0,height:0},$=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ee=$[V],ne=$[I],re=we(0,T[N],Z[N]),oe=O?T[N]/2-G-re-ee-H.mainAxis:X-re-ee-H.mainAxis,ie=O?-T[N]/2+G+re+ne+H.mainAxis:Y+re+ne+H.mainAxis,ae=t.elements.arrow&&E(t.elements.arrow),se=ae?"y"===j?ae.clientTop||0:ae.clientLeft||0:0,fe=null!=(C=null==B?void 0:B[j])?C:0,ce=F+ie-fe,pe=we(h?a(U,F+oe-fe-se):U,F,h?i(_,ce):_);L[j]=pe,R[j]=pe-F}if(c){var le,ue="x"===j?S:k,de="x"===j?P:A,me=L[D],he="y"===D?"height":"width",ve=me+b[ue],ye=me-b[de],ge=-1!==[S,k].indexOf(w),be=null!=(le=null==B?void 0:B[D])?le:0,xe=ge?ve:me-T[he]-q[he]-be+H.altAxis,Oe=ge?me+T[he]+q[he]-be-H.altAxis:ye,je=h&&ge?function(e,t,n){var r=we(e,t,n);return r>n?n:r}(xe,me,Oe):we(h?xe:ve,me,h?Oe:ye);L[D]=je,R[D]=je-me}t.modifiersData[r]=R}},requiresIfExists:["offset"]};var Oe={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=z(n.placement),f=Q(s),c=[k,A].indexOf(s)>=0?"height":"width";if(i&&a){var p=function(e,t){return $("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ee(e,L))}(o.padding,n),l=g(i),u="y"===f?S:k,d="y"===f?P:A,m=n.rects.reference[c]+n.rects.reference[f]-a[f]-n.rects.popper[c],h=a[f]-n.rects.reference[f],v=E(i),y=v?"y"===f?v.clientHeight||0:v.clientWidth||0:0,b=m/2-h/2,w=p[u],x=y-l[c]-p[d],O=y/2-l[c]/2+b,j=we(w,O,x),D=f;n.modifiersData[r]=((t={})[D]=j,t.centerOffset=j-O,t)}},effect:function(e){var t=e.state,n=e.options.element,o=void 0===n?"[data-popper-arrow]":n;null!=o&&("string"!=typeof o||(o=t.elements.popper.querySelector(o)))&&(r(o)||console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).',"To use an SVG arrow, wrap it in an HTMLElement that will be used as","the arrow."].join(" ")),G(t.elements.popper,o)?t.elements.arrow=o:console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper',"element."].join(" ")))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function je(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Ee(e){return[S,A,P,k].some((function(t){return e[t]>=0}))}var Se={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=te(t,{elementContext:"reference"}),s=te(t,{altBoundary:!0}),f=je(a,r),c=je(s,o,i),p=Ee(f),l=Ee(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":l})}},Pe=ie({defaultModifiers:[se,fe,le,ue]}),Ae=[se,fe,le,ue,de,be,xe,Oe,Se],ke=ie({defaultModifiers:Ae});e.applyStyles=ue,e.arrow=Oe,e.computeStyles=le,e.createPopper=ke,e.createPopperLite=Pe,e.defaultModifiers=Ae,e.detectOverflow=te,e.eventListeners=se,e.flip=be,e.hide=Se,e.offset=de,e.popperGenerator=ie,e.popperOffsets=fe,e.preventOverflow=xe,Object.defineProperty(e,"__esModule",{value:!0})}));