from django.http import HttpResponse
from .models import Pago, Venta
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import cm, mm, inch
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from io import BytesIO
from datetime import datetime

from decimal import Decimal

class ComprobanteCredito():
    def __init__(self, f):
        self.buf = BytesIO()
        self.venta = Venta.objects.get(factura=f)

    def run(self):
        self.doc = SimpleDocTemplate(self.buf, title=f"Comprobante {self.venta.factura}", pagesize=A4)
        self.story = []
        self.encabezado()
        self.crearTablaAbonos()
        self.doc.build(self.story, onFirstPage=self.numeroPagina, onLaterPages=self.numeroPagina)
        pdf = self.buf.getvalue()
        self.buf.close()
        return pdf

    def encabezado(self):
        # Encabezado con los detalles de la factura
        descuento_info = []
        imagen_logo = Image('Venta/logo.jpg', width=95, height=50, hAlign='RIGHT')
        
        a = Paragraph(f"Comprobante: {self.venta.factura}", self.estiloPC2())
        a1 = Paragraph(f"Cliente: {self.venta.nombre}", self.estiloPC2())
        a2 = Paragraph(f"Total Deuda: Q{self.venta.total}", self.estiloPC2())
        a3 = Paragraph(f"Fecha Deuda: {self.venta.fecha.strftime('%d/%m/%Y')}", self.estiloPC2())
        a4 = Paragraph(f"Fecha de Impresión: {datetime.today().strftime('%d/%m/%Y')}", self.estiloPC3())
        
        self.story.append(imagen_logo)
        self.story.append(a)
        self.story.append(a1)
        self.story.append(a2)
        
        # Agregar los detalles del descuento si existen en el último pago
        ultimo_pago = Pago.objects.filter(factura=self.venta).order_by('-fecha').first()
        if ultimo_pago:
            descuento_porcentaje = ultimo_pago.descuento_porcentaje or 0  # Usa 0 si es None
            if descuento_porcentaje > 0:
                descuento_porcentaje = ultimo_pago.descuento_porcentaje
                descuento_cantidad = ultimo_pago.descuento_cantidad
                nuevo_total = self.venta.total - descuento_cantidad

                descuento_info = [
                    Paragraph(f"Descuento Aplicado: {descuento_porcentaje}%", self.estiloPC2()),
                    Paragraph(f"Descuento en Cantidad: Q{descuento_cantidad}", self.estiloPC2()),
                    Paragraph(f"Nuevo Total: Q{nuevo_total}", self.estiloPC2())
                ]
            self.story.extend(descuento_info)

        
        self.story.append(a3)
        self.story.append(a4)
        self.story.append(Spacer(1, 0.2 * inch))

    def crearTablaAbonos(self):
        
        # Obtener el último pago para verificar si hay un descuento
        ultimo_pago = Pago.objects.filter(factura=self.venta).order_by('-fecha').first()
        descuento_aplicado = False

        # Verificar si hay un pago y si el descuento es mayor a 0, asegurándose de que no sea None
        if ultimo_pago and ultimo_pago.descuento_porcentaje and ultimo_pago.descuento_porcentaje > 0:
            descuento_aplicado = True

        
        # Tabla que muestra los abonos realizados
        abonos = Pago.objects.filter(factura=self.venta)
        data = [["Fecha", "Monto Abonado"]] + [
            [abono.fecha.strftime('%d/%m/%Y'), f"Q{abono.abono}"] for abono in abonos
        ]

        total_abonos = sum([abono.abono for abono in abonos])
        if descuento_aplicado:
            saldo_restante = 0
        else:
            # Si no hay descuento, calcular el saldo restante normalmente
            saldo_restante = self.venta.total - total_abonos

        data += [
            ["Total Abonado", f"Q{total_abonos}"],
            ["Saldo Restante", f"Q{saldo_restante}"]
        ]

        table = Table(data, colWidths=[4 * cm, 4 * cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black)
        ]))

        self.story.append(table)

    def estiloPC2(self):
        return ParagraphStyle(name='izquierda', fontName="Helvetica", fontSize=10, alignment=0)

    def estiloPC3(self):
        return ParagraphStyle(name='derecha', fontName="Helvetica", fontSize=10, alignment=2)

    def numeroPagina(self, canvas, doc):
        num = canvas.getPageNumber()
        canvas.drawRightString(200 * mm, 20 * mm, f"Página {num}")
