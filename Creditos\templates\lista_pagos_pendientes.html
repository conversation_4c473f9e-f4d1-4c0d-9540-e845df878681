{% extends 'BaseInicio/base.html' %}

{% block content %}
<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>

    <a href="{% url 'lista_ventas_pagadas' %}" class="btn btn-warning">Historial de Ventas Pagadas</a>
    <br></br>
    <h2>Ventas a Credito</h2>
    <form method="GET" action="{% url 'ListaPagoPendiente' %}">
        <input type="text" name="q" class="form-control" placeholder="Buscar por NIT, Nombre o Direccion" value="{{ query }}">
        <button type="submit" class="btn btn-primary mt-2">Buscar</button>
    </form>
    
    <table class="table table-striped mt-4">
        <thead>
            <tr>
                <th>NIT</th>
                <th>Factura</th>
                <th>Nombre</th>
                <th>Direccion</th>  
                <th>Venta</th>
                <th>Abono Total</th>
                <th>Saldo</th>
                <th>Ver</th>
                <th>Abonar</th>
                <th>PDF</th>
            </tr>
        </thead>
        <tbody>
            {% for item in ventas_con_abonos %}
            <tr>
                <td>{{ item.venta.nit }}</td>
                <td>{{ item.venta.factura }}</td>
                <td>{{ item.venta.nombre }}</td>
                <td>{{ item.venta.direccion }}</td>
                <td>Q{{ item.venta.total }}</td>
                <td>Q{{ item.abono_total }}</td>                
                <td>Q{{ item.queda }}</td>
                <td>
                    <button class="btn btn-success btn-sm" data-toggle="modal" 
                    data-target="#ventaModal" onclick="loadVentaDetail('{{ item.venta.pk }}')">
                Ver
                    </button>
                </td>
                <td>
                    <a href="{% url 'NuevoPago' item.venta.factura %}" class="btn btn-info">
                        <i class="fas fa-plus"></i>
                    </a>
                    
                    
                </td>
                <td>
                    <a href="{% url 'pdf_creditoVenta' item.venta.factura %}" class="btn btn-danger">
                        <i class="fas fa-file-pdf"></i>
                    </a>
                    
                </td>
             
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Modal para ver los detalles de la venta -->
<div class="modal fade" id="ventaModal" tabindex="-1" aria-labelledby="ventaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ventaModalLabel">Detalle de Venta</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Aquí se cargará el contenido de la venta -->
            </div>
        </div>
    </div>
</div>

<!-- Flatpickr para fechas -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        flatpickr('.flatpickr', {
            enableTime: false,
            dateFormat: "Y-m-d",
        });
    });

    function loadVentaDetail(ventaId) {
        fetch(`/venta/detalle_venta_cliente/${ventaId}/`)
            .then(response => response.text())
            .then(html => {
                document.getElementById('modal-body').innerHTML = html;
                $('#ventaModal').modal('show');
            });
    }
    

    function confirmDelete(ventaId) {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Obtener el csrf_token desde el cookie
                const csrftoken = getCookie('csrftoken');
        
                fetch(`/venta/eliminar/${ventaId}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken  // Agregar el CSRF token en los headers
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.deleted) {
                        Swal.fire('Eliminado', 'La venta ha sido eliminada.', 'success');
                        location.reload();  // Recargar la página para reflejar la eliminación
                    } else {
                        Swal.fire('Error', 'No se pudo eliminar la venta.', 'error');
                    }
                });
            }
        });
    }
    
    // Función para obtener el valor del csrf_token de las cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>


{% endblock %}
