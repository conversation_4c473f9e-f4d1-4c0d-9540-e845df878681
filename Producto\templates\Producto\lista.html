{% extends 'BaseInicio/base.html' %}
{% block title %}Lista Productos{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>


      <!-- Basic Layout -->
      <div class="col-md-14">
        <div class="card md-8">
          <div class="card-header d-flex align-items-center justify-content-between">
            <h5 class="mb-0">Listado de Productos</h5>
            <small class="text-muted float-end">Lista Productos</small>

          </div>
          <div class="card-body">
            <div class="table-responsive-sm">

               <input id="buscarInput" class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
            placeholder="Buscar.." style="border: 1px solid black;" name="buscar" autofocus="buscar"><br>

              
            <table class="table table-bordered order-table">
                <thead>
                  <tr>
                    
                    <th>Codigo </th>
                    <th>Producto</th>
                    <th>Categoria</th>
                    <th>Stock</th>
                    <th>Precio Compra</th>
                    <th>Precio Venta</th>
                    <th>Actions</th>
                    <th>Bitacora</th>
                  </tr>
                </thead>
                <tbody>
                  {% for p in prod %}
                  <tr>
                    
                    <td>{{p.codigo}}</td>
                    <td>{{p.nombre}}</td>
                    <td>{{p.id_cate}}</td>
                    <td>{{p.stock}}</td>
                    <td>Q.{{p.precio_compra}}</td>
                    <td>Q.{{p.precio_venta}}</td>
                    <td>
                      <a href="{% url 'UpdateProducto' p.id %}">
                        <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar">Modificar</i>
                      </a>
                      <a href="{% url  'DeleteProducto' p.id %}">
                        <i style="color: red;" class='bx bxs-trash' title="Eliminar">Eliminar</i>
                      </a>
                    </td>
                    <td>
                      <a href="{% url 'bitacora_producto' p.id %}">
  <i style="color: blue;" class='bx bx-history' title="Bitácora">Bitácora</i>
</a>

                    </td>
      
                  </tr>
                  {% empty %}
                  <caption>SIN PRODUCTOS</caption>
                  {% endfor %}

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  (function (document) {
      'use strict';

      var LightTableFilter = (function (Arr) {

          var _input;

          function _onInputEvent(e) {
              _input = e.target;
              var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
              Arr.forEach.call(tables, function (table) {
                  Arr.forEach.call(table.tBodies, function (tbody) {
                      Arr.forEach.call(tbody.rows, _filter);
                  });
              });
          }

          function _filter(row) {
              var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
              row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
          }

          return {
              init: function () {
                  var inputs = document.getElementsByClassName('light-table-filter');
                  Arr.forEach.call(inputs, function (input) {
                      input.oninput = _onInputEvent;
                  });
              }
          };
      })(Array.prototype);

      document.addEventListener('readystatechange', function () {
          if (document.readyState === 'complete') {
              LightTableFilter.init();
          }
      });

  })(document);
</script>


<script>
  // Lazy loading para productos
  let offset = 25; // Empezamos desde 25 porque ya cargamos los primeros 25
  let limit = 25;
  let loading = false;
  let searchTerm = '';
  let usingAjaxSearch = false;

  function cargarProductos(reset = false) {
      if (loading) return;
      loading = true;

      const params = new URLSearchParams({
          offset: reset ? 0 : offset,
          limit: limit,
          search: searchTerm
      });

      fetch(`?${params.toString()}`, {
          headers: { 'X-Requested-With': 'XMLHttpRequest' }
      })
      .then(res => res.json())
      .then(data => {
          const tbody = document.querySelector('.order-table tbody');
          if (reset) {
              tbody.innerHTML = '';
              offset = 0;
          }

          data.productos.forEach(prod => {
              const row = document.createElement('tr');
              row.innerHTML = `
                  <td>${prod.codigo}</td>
                  <td>${prod.nombre}</td>
                  <td>${prod.categoria}</td>
                  <td>${prod.stock}</td>
                  <td>Q.${prod.precio_compra}</td>
                  <td>Q.${prod.precio_venta}</td>
                  <td>
                      <a href="${prod.update_url}">
                          <i style="color: yellowgreen;" class='bx bxs-edit' title="Modificar">Modificar</i>
                      </a>
                      <a href="${prod.delete_url}">
                          <i style="color: red;" class='bx bxs-trash' title="Eliminar">Eliminar</i>
                      </a>
                  </td>
                  <td>
                      <a href="${prod.bitacora_url}">
                          <i style="color: blue;" class='bx bx-history' title="Bitácora">Bitácora</i>
                      </a>
                  </td>
              `;
              tbody.appendChild(row);
          });

          offset += data.productos.length;
          loading = false;
      })
      .catch(error => {
          console.error('Error:', error);
          loading = false;
      });
  }

  // Búsqueda con retraso (debounce) - solo AJAX
  let buscarTimeout = null;
  document.getElementById('buscarInput').addEventListener('input', (e) => {
      const inputValue = e.target.value.trim();

      if (inputValue.length > 0) {
          // Usar búsqueda AJAX
          usingAjaxSearch = true;
          searchTerm = inputValue;
          clearTimeout(buscarTimeout);
          buscarTimeout = setTimeout(() => {
              cargarProductos(true);
          }, 400);
      } else {
          // Volver al filtro local si no hay búsqueda
          usingAjaxSearch = false;
          searchTerm = '';
          // Mostrar todas las filas existentes
          const tbody = document.querySelector('.order-table tbody');
          Array.from(tbody.rows).forEach(row => {
              row.style.display = 'table-row';
          });
      }
  });

  // Scroll infinito - solo cuando no se está usando búsqueda AJAX
  window.addEventListener('scroll', () => {
      if (!usingAjaxSearch && (window.innerHeight + window.scrollY) >= document.body.offsetHeight - 200) {
          cargarProductos();
      }
  });
</script>
  
  
{% endblock %}