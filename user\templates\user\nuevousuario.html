{% extends 'BaseInicio/base.html' %}
{% load static %}

{% block content %}
{% if messages %}
          <ul class="messages">
          {% for message in messages %}
          <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <strong>{{message}}</strong>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          {% endfor %}
{% endif %}  
<br></br>
<br></br>
<div class="container">
<div class="card-box mb-30">
    <form action="" method="POST">{% csrf_token %}
        <br>
        <h3 class="text-primary" align="center">REGISTRO DE NUEVO USUARIO</h3>
        <div class="row" align="center" style="margin: 15px;">
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.username.label_tag}}</label>
                    {{form.username}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.password.label_tag}}</label>
                    {{form.password}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.first_name.label_tag}}</label>
                    {{form.first_name}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.last_name.label_tag}}</label>
                    {{form.last_name}}
                </div>
            </div>
        </div>
        <div class="row" align="center" style="margin: 15px;">
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.categoria.label_tag}}</label>
                    {{form.categoria}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.rol.label_tag}}</label>
                    {{form.rol}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.is_staff.label_tag}}</label>
                    {{form.is_staff}}
                </div>
            </div>
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.is_active.label_tag}}</label>
                    {{form.is_active}}
                </div>
            </div>
        </div>
        <div class="row" align="center" style="margin: 15px;">
            <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.is_superuser.label_tag}}</label>
                    {{form.is_superuser}}
                </div>
            </div>
           <div class="col-md-3 col-sm-12">
                <div class="form-group">
                    <label>{{form.asignacion.label_tag}}</label>
                    {{form.asignacion}}
                </div>
        </div>
        <br>
    <div class="container" align="center">
    <button type="submit" class="btn btn-primary">Guardar</button>
    <button type="reset" class="btn btn-danger">Cancelar</button>
    </div>
    <br>
    </form>
</div>
</div>

{% endblock %}