{% extends 'BaseInicio/base.html' %}
{% block title %}Venta{% endblock %}
{% block carta %}<i class="fa-solid fa-house"></i> Venta{% endblock %}
{% load static %}
{% block content %}

<!--<div class="col-md-6">
    {% if messages %}
    {% for message in messages %}
    <script>
        Swal.fire({
            "title": "Informacion Sistema",
            "text": "{{message}}",
            "icon": "{{message.tags}}"
        })
    </script>
    {% endfor %}
    {% endif %}
</div>-->
<br> <br>
<div class="container container-venta">
    
    <div class="row col-md" style="background-color: cadetblue; border-bottom: black 2px solid; padding-bottom: 1rem;"><br></br>
        <div class="form-label label-venta" style="display: flex; align-items: center; gap: 1rem;">
            <h4 style="margin: 0;">DATOS DE CLIENTE</h4>
        
            <div style="margin-left: auto;">
                <label for="TipoVenta" class="form-label" style="margin: 0; font-weight: bold; color: black; font-size: 1.2rem;">
                    DOCUMENTO:
                    <span style="font-weight: bold; color: black;">
                        <strong>
                            {% if tipo_venta == 'Cotizacion' %}
                                Cotización
                            {% elif tipo_venta == 'Contado' %}
                                Factura Electrónica
                            {% elif tipo_venta == 'Proforma' %}
                                Proforma
                            {% endif %}
                        </strong>
                        
                    </span>
                </label>
            </div>
       
   
        </div>
    
        <form action="#" method="POST">{% csrf_token %}
            <div class="row">
                <div class="col-md-4">
                    <label for="Nit" class="form-label">Nit:</label>
                    <input type="text" name="nit" class="form-control" value="{{venta.nit}}">
                </div>
                <div class="col-md-4">
                    <label for="Nombre" class="form-label">Nombre:</label>
                    <input type="text" name="nombre" class="form-control" value="{{venta.nombre}}">
                </div>
                <div class="col-md-4">
                    <label for="Direccion" class="form-label">Direccion:</label>
                    <input type="text" name="direccion" class="form-control" value="{{venta.direccion}}">
                </div>
                <br></br>
                <div class="col-md-12"><br>
                    <label for="credito"><h4>Venta a Crédito:</h4></label>
                    <input type="checkbox" name="credito" id="credito" 
                           style="transform: scale(2); -webkit-transform: scale(2); margin-left: 10px;"
                           {% if venta.estado == 3 %}checked{% endif %}>
                </div>
                <div class="col-md-12">
                    <div class="col-md" style="text-align: center;">
                        <button class="btn btn-info" name="terminar">Finalizar</button>
                        <a type="button" href="{% url 'Descartar' venta.token %}" class="btn btn-danger">Descartar</a>
                    </div>
                </div>
            </div>
        </form>
    </div>&nbsp;

    <div class="row col-md">
        <div class="row col-md-12" style="border-bottom: black 2px solid;">
            <label for="DatosVenta" class="form-label label-venta"></label>
            
            <div class="col-md-2">
                <label for="Mensaje" class="form-label">Venta:</label>
                <p style="display: inline;"><strong>{{ venta.factura }}</strong></p>
            </div>
            
            <div class="col-md-3">
                <label for="Fecha" class="form-label">Fecha:</label>
                <p style="display: inline;"><strong>{% now 'd-m-Y H:i:s' %}</strong></p>
            </div>
            
            <div class="col-md-4">
                <form action="#" method="POST" style="display: inline;">{% csrf_token %}
                    <div class="input-group">
                        <input type="text" name="descuento_total" class="form-control" placeholder="Descuento al total">
                        <button type="submit" class="btn btn-warning">Aplicar Descuento</button>
                    </div><br>
                </form>
            </div>
        </div>
        
        <div class="row col-md-12" style="border-bottom: black 2px solid;">
            <label for="DatosVenta" class="form-label label-venta"></label>
            
            <div class="col-md-4"><br>
                <label for="subtotal_descuento" class="form-label">Sub-Total:</label>
                <p style="display: inline;"><strong>Q.{{ subtalVenta }}</strong></p>
            </div>
            
            <div class="col-md-4"><br>
                <label for="DescuentoAplicado" class="form-label">Descuento:</label>
                <p style="display: inline;"><strong>Q.{{ venta.descuento_total }}</strong></p>
            </div>
            
            <div class="col-md-4"><br>
                <label for="Total" class="form-label">Total:</label>
                <h4 style="display: inline;"><strong><B><b>Q.{{ venta.total }}</b></strong></h4>
            </div>
        </div>
             
    </div>  <br>
    <div class="row col-md">
        <div class="col-md-12" style="border-bottom: black 2px solid;">
            <div class="col-md-12">
                <label for="Buscar">Buscar</label>
                <form action="#" method="POST">{% csrf_token %}
                    <input type="text" name="buscar" class="form-control" autofocus="buscar"
                        placeholder="Agregar/Buscar"><br>
                </form>
            </div>
            
            
    </div>

    <div class="col-md-12">
        <label for="ProdcutosAgregados" class="form-label label-venta">
            <h4>PRODUCTOS AGREGADOS</h4>
        </label>
        <div style="height: 15rem; overflow-y: scroll;">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th scope="col">Cod</th>
                            <th scope="col">Prod</th>
                            <th scope="col">Cant</th>
                            <th scope="col">Precio</th>
                            <th scope="col">Subtotal</th>
                            <th scope="col">Descuento</th>
                            <th scope="col">Total</th>
                            <th scope="col">Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for d in detalle %}
                        <tr>
                            <th scope="row">{{d.id_inventario.codigo}}</th>
                            <td>
                                {% if d.nombre_custom %}
                                    {{ d.nombre_custom }}
                                {% else %}
                                    {{ d.id_inventario.nombre }}
                                {% endif %}
                             </td>
                            <td>{{d.cantidad}}</td>
                            <td>
                                {% if d.precio_custom %}
                                    Q{{d.precio_custom}}
                                {% else %}
                                    Q{{d.precio_uni}}
                                {% endif %}
                            </td>
                            <td>Q{{d.subtotal}}</td>
                            <td>Q{{d.descuento}}</td>
                            <td>Q.{{d.total}}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#editModal{{d.id}}">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <form action="#" method="POST" style="display: inline;">
                                    {% csrf_token %}
                                    <input type="hidden" name="corr" value="{{d.id}}">
                                    <input type="hidden" name="id" value="{{d.id_inventario.id}}">
                                    <button type="submit" name="quitar" class="btn btn-sm btn-danger"><i class="fa fa-minus-circle"></i></button>
                                </form>
                            </td>
                        </tr>

                        <!-- Modal para editar producto -->
                        <div class="modal fade" id="editModal{{d.id}}" tabindex="-1" role="dialog" aria-labelledby="editModalLabel{{d.id}}" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="editModalLabel{{d.id}}">Editar Producto</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <form action="#" method="POST">
                                        {% csrf_token %}
                                        <div class="modal-body">
                                            <div class="form-group">
                                                <label for="nombre_producto" class="form-label">Nombre Producto</label>
                                                <input type="text" class="form-control" name="nombre_producto" value="{{d.id_inventario.nombre}}" >
                                            </div>
                                            <div class="form-group">
                                                <label for="precio_producto" class="form-label">Precio Producto</label>
                                                <input type="text" class="form-control" name="precio_producto" value="{{d.precio_uni}}">
                                            </div>
                                            <input type="hidden" name="corr" value="{{d.id}}">
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                                            <button type="submit" class="btn btn-primary" name="editar">Guardar Cambios</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <tr>
                            <td colspan="8">SIN PRODUCTOS</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para resultados de búsqueda -->
<div class="modal fade" id="buscarModal" tabindex="-1" role="dialog" aria-labelledby="buscarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="buscarModalLabel">Resultados de Búsqueda</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Aquí se incluye la tabla de resultados -->
                <div style="overflow-y: auto; max-height: 70vh;">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th scope="col">Cod</th>
                                    <th scope="col">Prod</th>
                                    <th scope="col">Stock</th>
                                    <th scope="col">Precio</th>
                                    <th scope="col">Cant</th>
                                    <th scope="col">Desc</th>
                                    <th scope="col">Acción</th>
                                </tr>
                            </thead>
                            <tbody id="resultadosBusqueda">
                                <!-- Se insertan dinámicamente los resultados -->
                                {% for busq in busq %}
                                <tr>
                                    <td>{{ busq.codigo }}</td>
                                    <td>{{ busq.nombre }}</td>
                                    <td>{{ busq.stock }}</td>
                                    <td>Q{{ busq.precio_venta }}</td>
                                    <form action="#" method="POST">{% csrf_token %}
                                        <input type="hidden" value="{{ busq.id }}" name="id">
                                        <td>
                                            {% if busq.stock > 0 %}
                                            <input type="number" name="cantidad" min="1" max="{{ busq.stock }}" value="">
                                            {% else %}
                                            <input type="number" name="cantidad" value="0" readonly disabled>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <input type="text" name="descuento" placeholder="0" size="5" value="0">
                                        </td>
                                        <td>
                                            {% if busq.stock > 0 %}
                                            <button class="btn btn-success btn-sm" type="submit" name="agregar">
                                                <i class="fa fa-cart-plus"></i> Agregar
                                            </button>
                                            {% else %}
                                            <button class="btn btn-secondary btn-sm" disabled>Sin Stock</button>
                                            {% endif %}
                                        </td>
                                    </form>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Para los botones que abren modales
        $('[data-toggle="modal"]').on('click', function () {
            var targetModal = $($(this).data('target'));
            targetModal.modal('show');
        });
    
        // Para mostrar el modal de búsqueda automáticamente
        {% if b %}
        $('#buscarModal').modal('show');
        {% endif %}
        
        // Cerrar modal al hacer clic en "Agregar"
        $('form button[name="agregar"]').on('click', function () {
            $('#buscarModal').modal('hide');
        });
    });
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('[data-toggle="modal"]').forEach(function (btn) {
            btn.addEventListener('click', function () {
                var modal = document.querySelector(this.getAttribute('data-target'));
                if (modal) {
                    var modalInstance = new bootstrap.Modal(modal);
                    modalInstance.show();
                }
            });
        });
    });
    

    $(document).ready(function () {
        {% if b %}
        $('#buscarModal').modal('show');
        {% endif %}
        
        // Cerrar modal al hacer clic en "Agregar"
        $('form button[name="agregar"]').on('click', function () {
            $('#buscarModal').modal('hide');
        });
    });
    

</script>


<style>
    /* Estilos para el modal */
    @media (min-width: 992px) {
        .modal-xl {
            max-width: 90vw; /* Hace el modal más ancho */
        }
    }

    .modal-dialog {
        max-width: 90vw;
        margin: 1.75rem auto;
    }
    .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }

    /* Personaliza la barra de scroll */
    .modal-body::-webkit-scrollbar {
        width: 8px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Asegura que la tabla sea responsive dentro del modal */
    .modal .table-responsive {
        margin-bottom: 0;
    }
</style>

{% endblock %}

