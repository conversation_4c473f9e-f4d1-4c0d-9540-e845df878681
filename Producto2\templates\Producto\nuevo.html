{% extends 'BaseInicio/base.html' %}
{% block title %}Ingreso Producto{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}

<div class="content-wrapper">

    {{form.errors}}

    <br></br>
    <div class="container" style="background-color: dark; margin-top: 5px; max-width: 70%;"><br>
    
       <div class="row">
            <!-- Basic Layout -->
            <div class="col-md-12">
                <div class="card md-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">Formulario Ingreso de Productos</h5>
                        <small class="text-muted float-end">Producto</small>
                    </div>
                    <div class="card-body">
                        <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}

                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">Codigo:</label>
                                    {{form.codigo}}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Nombre:</label>
                                    {{form.nombre}}
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Descripcion:</label>
                                    {{form.descripcion}}
                                </div>
                            </div><br>


                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Exitencia:</label>
                                    {{form.stock}} 
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Ingreso:</label>
                                    {{form.ingreso}}
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Precio Compra:</label>
                                    {{form.precio_compra}}
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Precio Venta:</label>
                                    {{form.precio_venta}}
                                </div>
                            </div><br>
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="">Fecha Ingreso</label>
                                    <input type="text" class="form-control" value="{% now 'd-m-Y' %}" style="border: 1px solid; color: black;" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="">Usuario</label>
                                    <input type="text" class="form-control" value="{{user.username}}" style="border: 1px solid; color: black;" readonly>
                                </div>

                            </div><br>

                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">Guardar</button>
                                    <button type="reset" class="btn btn-danger">Cancelar</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>

{% endblock %}