from django.conf import settings
from django.db import models
from Venta.models import Venta
import uuid


class Pago(models.Model):
    factura = models.ForeignKey(Venta, on_delete=models.CASCADE)
    tipo = models.CharField(max_length=75)
    nit = models.CharField(max_length=15, blank=True, null=True, default='')
    abono = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    estado = models.IntegerField(default=1)
    fecha = models.DateField()
    usuario = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    token = models.UUIDField(default=uuid.uuid4, editable=False)

    # Nuevos campos para el descuento
    descuento_porcentaje = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, default=0)
    descuento_cantidad = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0)

    class Meta:
        ordering = ["factura"]

    def __str__(self):
        return str(self.factura)
