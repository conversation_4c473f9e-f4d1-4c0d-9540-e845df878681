{% extends 'BaseInicio/base.html' %}
{% block title %}Lista Ventas de Hoy{% endblock %}
{% block carta %}<i class="fa-solid fa-box-open"></i> Lista Ventas de Hoy {% endblock %}
{% load static %}

{% block content %}
<br>
<div class="container">
    <div class="col-md-12 container-table">
        <div class="tile">
            <a href="{% url 'Venta' %}"><button class="btn btn-primary">Nueva Venta</button></a>
            <div class="container"><br></div>
            <h3 class="tile-title">Lista Ventas Hoy</h3>
            <div class="row">
                <div class="col-md-12" align="right">
                    <a href="{% url 'ListaVentaTodas' %}" class="btn btn-warning">Todas Las Ventas</a>
                </div>
            </div><br>

            <input class="form-control col-md-3 light-table-filter" data-table="order-table" type="text"
                   placeholder="Buscar..." style="border: 1px solid black;" name="buscar" autofocus>

            <div class="table-responsive">
                <table class="table table-bordered order-table" style="font-size: 14px;">
                    <thead>
                        <tr>
                            <th>Venta #</th>
                            <th>Nombre de Cliente</th>
                            <th>Total</th>
                            <th>Estado</th>
                            <th>Tipo</th>
                            <th>Fecha</th>
                            <th>Usuario</th>
                            <th>Acción</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for p in hoy %}
                        <tr>
                            <td>{{ p.factura }}</td>
                            <td>{{ p.nombre }}</td>
                            <td>Q.{{ p.total }}</td>
                            <td>
                                {% if p.estado == 1 %}Terminada
                                {% elif p.estado == 3 %}Credito
                                {% elif p.estado == 4 %}Pendiente/Sin Terminar
                                {% elif p.estado == 2 %}Anulada
                                {% else %}Desconocido
                                {% endif %}
                            </td>
                            <td>
                                {% if p.tipo == "Contado" %}
                                    <span class="badge badge-info">FEL</span>
                                {% elif p.tipo == "Proforma" %}
                                    <span class="badge badge-success">Proforma</span>
                                {% else %}
                                    <span class="badge badge-warning">Proforma Crédito</span>
                                {% endif %}
                            </td>
                            <td>{{ p.fecha|date:"d-m-Y" }}</td>
                            <td>{{ p.usuario.username }}</td>
                            <td>
                                {% if p.estado == 2 %}
                                    {# Venta anulada - botón para descargar PDF #}
                                    {% if p.token %}
                                        <a href="{% url 'PDF' p.token %}" target="_blank" rel="noopener noreferrer">
                                            <button class="btn btn-danger btn-sm" title="Factura Anulada - Descargar PDF">
                                                <i class="fa fa-file-pdf"></i> <b>-Factura Anulada</b>
                                            </button>
                                        </a>
                                    {% else %}
                                        <button class="btn btn-danger btn-sm" disabled title="PDF no disponible">
                                            <i class="fa fa-file-pdf"></i> <b>-Factura Anulada</b>
                                        </button>
                                    {% endif %}
                                {% else %}
                                    {# Para otros estados y tipos de venta #}
                                    {% if p.tipo != "Contado" %}
                                        {% if p.token %}
                                            <a href="{% url 'Detalle' p.token %}">
                                                <button class="btn btn-warning btn-sm" title="Modificar">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                            </a>
                                            <a href="{% url 'PasarFEL' p.token %}">
                                    <button class="btn btn-secondary btn-sm">
                                    <b>A FEL</b></button>
                                    </a>
                                        {% else %}
                                            <button class="btn btn-warning btn-sm" disabled title="No disponible">Modificar</button>
                                        {% endif %}
                                    {% endif %}

                                    {% if p.tipo == "Contado" %}
                                        {% if p.token %}
                                            <a href="{% url 'Detalle' p.token %}">
                                                <button class="btn btn-warning btn-sm" title="Modificar">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-warning btn-sm" disabled title="No disponible">Modificar</button>
                                        {% endif %}
                                        {% if p.link %}
                                            <a href="{{ p.link }}" target="_blank" rel="noopener noreferrer">
                                                <button class="btn btn-info btn-sm" title="Descargar FEL">
                                                    <i class="fa fa-file-pdf"></i> <b>FEL</b>
                                                </button>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-info btn-sm" disabled title="FEL no disponible">FEL</button>
                                        {% endif %}
                                        {% if p.token %}
                                            <a href="{% url 'AnulaFEL' p.token %}">
                                                <button class="btn btn-danger btn-sm" title="Anular FEL">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-danger btn-sm" disabled title="No disponible">Anular FEL</button>
                                        {% endif %}
                                    {% else %}
                                        {% if p.token %}
                                            <a href="{% url 'PDF' p.token %}" target="_blank" rel="noopener noreferrer">
                                                <button class="btn btn-success btn-sm" title="Descargar PDF">
                                                    <i class="fa fa-file-pdf"></i>
                                                </button>
                                            </a>
                                            <a href="{% url 'Anular' p.token %}" title="Anular venta">
                                                <button class="btn btn-danger btn-sm">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-success btn-sm" disabled title="PDF no disponible">PDF</button>
                                            <button class="btn btn-danger btn-sm" disabled title="No disponible">Anular</button>
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">SIN VENTAS HOY</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    (function (document) {
        'use strict';
        var LightTableFilter = (function (Arr) {
            var _input;

            function _onInputEvent(e) {
                _input = e.target;
                var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
                Arr.forEach.call(tables, function (table) {
                    Arr.forEach.call(table.tBodies, function (tbody) {
                        Arr.forEach.call(tbody.rows, _filter);
                    });
                });
            }

            function _filter(row) {
                var text = row.textContent.toLowerCase(),
                    val = _input.value.toLowerCase();
                row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
            }

            return {
                init: function () {
                    var inputs = document.getElementsByClassName('light-table-filter');
                    Arr.forEach.call(inputs, function (input) {
                        input.oninput = _onInputEvent;
                    });
                }
            };
        })(Array.prototype);

        document.addEventListener('readystatechange', function () {
            if (document.readyState === 'complete') {
                LightTableFilter.init();
            }
        });
    })(document);
</script>

{% endblock %}
