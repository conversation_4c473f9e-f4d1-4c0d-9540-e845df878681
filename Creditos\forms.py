from django import forms
from .models import Pago

TIPO = (('ABONO', 'ABONO'), ('TOTALIDAD', 'TOTALIDAD'))

class PagoForm(forms.ModelForm):
    class Meta:
        model = Pago
        fields = ['tipo', 'abono']
        widgets = {
            'tipo': forms.Select(attrs={'class': 'selectpicker form-control', 'data-style': 'btn-outline-info'}, choices=TIPO),
            'abono': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '0.00'}),
        }
