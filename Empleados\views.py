from decimal import Decimal, InvalidOperation
from django.shortcuts import render,redirect
from Empleados.models import <PERSON><PERSON>,Planilla
from Empleados.forms import UpdatePersonaForm
from datetime import datetime
from django.contrib import messages
from decimal import Decimal

from Empleados import models

def inicio(request):
    return render(request,'Empleados/inicio.html')



def nuevo(request):
    if request.method == "POST":
        dpi = request.POST.get('dpi')
        
        # Verificar si el DPI ya existe
        if Persona.objects.filter(dpi=dpi).exists():
            messages.error(request, 'El DPI ya existe. Por favor, ingresa otro.')
            return render(request, 'Empleados/nuevo.html', {'form_data': request.POST})

        # Si el DPI no existe, guardar la nueva persona
        p = Persona(
            dpi=request.POST.get('dpi'),
            nombre=request.POST.get('nombre'),
            apellido=request.POST.get('apellido'),
            direccion=request.POST.get('direccion'),
            telefono=request.POST.get('telefono'),
            puesto=request.POST.get('puesto'),
            salario_base=request.POST.get('salario_base'),
            contacto_emergencia=request.POST.get('contacto_emergencia'),
            telefono_contacto=request.POST.get('telefono_contacto'),
            estado_civil=request.POST.get('estado_civil'),
            fecha_inicio=datetime.today(),
            fecha_baja='',
            fecha=datetime.today().strftime('%Y-%m-%d'),
            estado=1
        )
        p.save()
        messages.success(request, f'Empleado {p.nombre} {p.apellido} ingresado con éxito!')
        return redirect('Listado')

    return render(request, 'Empleados/nuevo.html')



def modificar(request,id):
    persona = Persona.objects.get(dpi=id)
    if request.method == 'GET':
        form = UpdatePersonaForm(instance=persona)
    else:
        form = UpdatePersonaForm(request.POST,instance = persona)
     
        if form.is_valid():
            try:
                persona.fecha = str(datetime.today().strftime('%d-%m-%Y'))
                form.save()
                messages.success(request, f'Empleado {persona.nombre} Modificado Exitosamente!')
                return redirect('Listado')
            except:
                messages.error(request, f'No Se Pudo Modificar {persona.nombre}!')
                return redirect('Listado')
    return render(request,'Empleados/modificar.html',{'form':form})


def eliminar(request,id):
    try:
        Planilla.objects.filter(dpi=id).delete()
        Persona.objects.get(dpi=id).delete()
        messages.success(request,'Empleado Borrado')
        return redirect('Listado')  
    except:
        messages.error(request,'Empleado NO Borrado')
        return redirect('Listado')    

def lista(request):
    emp = Persona.objects.all()
    return render(request,'Empleados/lista.html',{'emp':emp})



######################################## PLANILLAS #############################################

from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from .models import PlanillaBase, Planilla, Persona
from .forms import AgregarTrabajadorForm,PlanillaForm

@login_required
def crear_planilla_y_agregar_trabajadores(request):
    if request.method == "POST":
        # Crear la planilla base
        planilla_base = PlanillaBase.objects.create(
            nombre_planilla=request.POST['nombre_planilla'],
            mes=request.POST['mes'],
            ciclo=request.POST['ciclo']
        )
        
        # Redirigir a la misma vista para agregar trabajadores a la planilla recién creada
        return redirect('agregar_trabajador', planilla_base.id)

    return render(request, 'Planilla/crear_planilla.html')


from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404, redirect
from django.db.models import Q
from django.contrib import messages
from decimal import Decimal, InvalidOperation
from .models import PlanillaBase, Persona, Planilla

@login_required
def agregar_trabajador(request, planilla_id):
    planilla = get_object_or_404(PlanillaBase, id=planilla_id)
    trabajadores = Persona.objects.none()  # Inicia como queryset vacío
    salario_base = Decimal('0.00')
    sueldo_liquido = Decimal('0.00')
    pla = Planilla.objects.filter(planilla_base=planilla_id)
    form = PlanillaForm()

    if request.method == "POST":
        
        if 'buscar_empleado' in request.POST:
            
            if request.POST['tipo'] == 'DPI':
                try:
                    trabajadores = Persona.objects.get(dpi=str(request.POST['buscar']))
                    return render(request, 'Planilla/agregar_trabajador.html', {'pla':pla,'planilla': planilla,'trabajador': trabajadores,'form':form})
                except:
                    messages.error(request,'Empleado No Encontrado') 
                    return redirect('agregar_trabajador', planilla_id)
            else:
                try:
                    trabajadores = Persona.objects.get(Q(nombre__icontains=request.POST['buscar']) | Q(apellido__icontains=request.POST['buscar']))
                    return render(request, 'Planilla/agregar_trabajador.html', {'pla':pla,'planilla': planilla,'trabajador': trabajadores,'form':form})
                except:
                    messages.error(request,'Empleado No Encontrado') 
                    return redirect('agregar_trabajador', planilla_id)

        elif 'agregar' in request.POST:
            form = PlanillaForm(request.POST)
            dpi_emp = request.POST['dpi']
            emp = Persona.objects.get(dpi=dpi_emp)
            #try:
            if form.is_valid():
                p = Planilla()
                p.planilla_base = PlanillaBase(id=planilla_id)
                p.dpi = Persona.objects.get(dpi=request.POST['dpi'])
                p.salario_base = emp.salario_base
                p.bonificacion = form.cleaned_data['bonificacion']
                p.hora_extra = form.cleaned_data['hora_extra']
                p.otras_deducciones = form.cleaned_data['otras_deducciones']
                p.liquido = form.cleaned_data['liquido']
                p.save()
                messages.success(request,f'Acreditacion de Salario a Empleado {p.dpi.nombre} Exitoso!')
                return redirect('agregar_trabajador', planilla_id)
            else:
                messages.error(request,f'Error en acreditacion de Salario a Empleado!')
                return redirect('agregar_trabajador', planilla_id)      
            #except:
                #messages.error(request,f'Error en acreditacion de Salario a Empleado {p.dpi.nombre}!')
                #return redirect('agregar_trabajador', planilla_id)           

            


        




    return render(request, 'Planilla/agregar_trabajador.html', {'pla':pla,'planilla': planilla,'trabajadores': trabajadores,'salario_base': salario_base,'sueldo_liquido': sueldo_liquido,'form':form})




from django.http import JsonResponse
from django.db.models import Q  # Asegúrate de importar Q desde django.db.models
from .models import Persona

def buscar_empleado(request):
    buscar_empleado = request.GET.get("buscar_empleado", "").strip()  # Limpia cualquier espacio en blanco

    if not buscar_empleado:
        return JsonResponse({"success": False, "message": "No se ingresó un valor para buscar."})

    try:
        # Busca por DPI o Nombre
        empleado = Persona.objects.get(Q(dpi=buscar_empleado) | Q(nombre__icontains=buscar_empleado))
        
        # Retorna los datos como JSON
        return JsonResponse({
            "success": True,
            "nombre": empleado.nombre,
            "apellido": empleado.apellido,  # Asegúrate de que el modelo tiene el campo apellido
            "sueldo": empleado.salario_base,  # Asegúrate de que el modelo tiene el campo salario_base
        })
    except Persona.DoesNotExist:
        return JsonResponse({"success": False, "message": "Empleado no encontrado."})
    except Exception as e:
        return JsonResponse({"success": False, "message": str(e)})  # Manejo de errores genérico





def eliminarplanilla(request,id):
    try:
        Planilla.objects.filter(planilla_base=id).delete()
        PlanillaBase.objects.filter(id=id).delete()
        messages.success(request,'Planilla Borrada')
        return redirect('ListadoPlanilla')  
    except:
        messages.error(request,'Planilla NO Borrado')
        return redirect('ListadoPlanilla')    


from django.shortcuts import render
from .models import PlanillaBase

def listaplanilla(request):
    planillas = PlanillaBase.objects.all()
    planillas_con_totales = []
    for planilla in planillas:
        total_sueldos = planilla.calcular_total_sueldos()  # Asegúrate de que este método esté en tu modelo PlanillaBase
        planillas_con_totales.append({'planilla': planilla, 'total_sueldos': total_sueldos})
    
    return render(request, 'Planilla/lista.html', {'planillas_con_totales': planillas_con_totales})
