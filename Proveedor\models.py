from django.db import models
from user.models import User

class Proveedor(models.Model):
    nit = models.CharField(max_length=15,blank=False,null=False)
    nombre = models.CharField(max_length=250,blank=False,null=False)
    direccion = models.CharField(max_length=550,blank=False,null=False)
    telefono = models.CharField(max_length=9,blank=False,null=False)
    estado = models.IntegerField(blank=False,null=False,default=1)
    fecha = models.DateField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)

    class Meta:
        ordering = ['nit']

    def __str__(self):
        return self.nit    