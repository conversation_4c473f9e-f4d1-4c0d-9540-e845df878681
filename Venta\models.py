from django.db import models
from user.models import User
from Producto.models import Producto
import uuid
from datetime import datetime
from django.utils import timezone

def generate_uuid_without_hyphens():
    """Genera un UUID sin guiones para mantener compatibilidad con datos existentes"""
    return uuid.uuid4().hex

class Venta(models.Model):
    factura = models.BigAutoField(primary_key=True,blank=False,null=False)
    nit = models.CharField(max_length=15,blank=True,null=True,default='CF')
    nombre = models.CharField(max_length=350,blank=True,null=True,default='Consumidor Final')
    direccion = models.CharField(max_length=350,blank=True,null=True,default='Ciudad')
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    #descuento_total sirve para guardar un descuento extra al total de la venta
    descuento_total = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True, default=0.00) 
    estado = models.IntegerField(blank=False,null=False,default=0)
    fecha = models.DateField(blank=False,null=False)
    
    tipo = models.CharField(max_length=250,blank=True,null=True)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.CharField(max_length=32, default=generate_uuid_without_hyphens, editable=False)
    link = models.CharField(max_length=250,blank=True,null=True)# link pdf factura fel
    numero = models.BigIntegerField(blank=True,null=True)
    serie = models.CharField(max_length=250,blank=True,null=True)
    anula = models.CharField(max_length=450,blank=True,null=True)
    fecha_fel = models.CharField(max_length=250,blank=True,null=True)
    
    ESTADO_PAGADA = 1
    ESTADO_CREDITO = 3
    ESTADO_CANCELADA = 4  # Agregar este estado

    ESTADOS = (
        (ESTADO_PAGADA, 'Pagada'),
        (ESTADO_CREDITO, 'A Crédito'),
        (ESTADO_CANCELADA, 'Cancelada'),  # Nuevo estado
    )
    


    class Meta:
        ordering = ["factura"]
        indexes = [
            models.Index(fields=['estado']),
            models.Index(fields=['fecha']),
            models.Index(fields=['-factura']),
            models.Index(fields=['estado', 'fecha']),
            models.Index(fields=['token']),
            models.Index(fields=['tipo']),
        ]

    def __str__(self):
        return str(self.factura)
    



class Detalle(models.Model):
    factura = models.ForeignKey(Venta,on_delete=models.CASCADE,blank=False,null=False)
    id_inventario = models.ForeignKey(Producto,on_delete=models.CASCADE,blank=False,null=False)
    
    #para guardar el nuevo nombre o precio del producto si se cambia
    nombre_custom = models.CharField(max_length=250, blank=True, null=True)  
    precio_custom = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)  
    
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    precio_uni = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    descuento = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    subtotal = models.DecimalField(max_digits=12,decimal_places=2,blank=False,null=False,default=0.00)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    estado = models.IntegerField(blank=False,null=False,default=1)
    fecha = models.DateTimeField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.CharField(max_length=32, default=generate_uuid_without_hyphens, editable=False)

    class Meta:
        ordering = ["factura"]

    def __str__(self):
        return str(self.factura)
    




class Gasto(models.Model):
    nombre = models.CharField(max_length=500,blank=False,null=False)
    cantidad = models.IntegerField(blank=False,null=False,default=0)
    precio_uni = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    total = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    estado = models.IntegerField(blank=False,null=False,default=1)
    fecha = models.DateTimeField(default=timezone.now)
    usuario = models.CharField(max_length=500,blank=False,null=False)

    class Meta:
        ordering = ["id"]

    def __str__(self):
        return str(self.id)
        
        

from django.db import models

class Abono(models.Model):
    venta = models.ForeignKey(Venta, on_delete=models.CASCADE)
    monto = models.DecimalField(max_digits=12, decimal_places=2)
    fecha = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Abono de {self.monto} para la venta {self.venta.factura}"


class PagoCreditoFel(models.Model):
    venta = models.ForeignKey(Venta, on_delete=models.CASCADE, related_name='pagos_credito_fel')
    fecha_pago = models.DateField(auto_now_add=True)
    monto_pagado = models.DecimalField(max_digits=12, decimal_places=2)
    usuario = models.ForeignKey(User, on_delete=models.CASCADE)
    observaciones = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-fecha_pago']
        verbose_name = 'Pago Crédito FEL'
        verbose_name_plural = 'Pagos Crédito FEL'

    def __str__(self):
        return f"Pago FEL #{self.venta.factura} - Q.{self.monto_pagado} - {self.fecha_pago}"


