from django import forms
from .models import Producto
from .models import Producto,ProductoFactura



class ProductoForm(forms.ModelForm):

    class Meta:
        model = Producto
        fields = ['codigo','nombre', 'descripcion', 'stock',
                  'precio_compra','precio_venta','ingreso']

        widgets = {
            'codigo': forms.TextInput(attrs={'class': 'form-control','placeholder':'  Codigo','autofocus': True,'require':True,'style':'border: 1px solid; color:black;'}),
            'nombre': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Nombre de Producto', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'descripcion': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Descripcion de Producto', 'style': 'color: black', 'require': False}),
            'stock': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_compra': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_venta': forms.TextInput(attrs={'name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'ingreso': forms.TextInput(attrs={'class': 'form-control','placeholder':'  Ingreso','require':True,'style':'border: 1px solid; color:black;'}),
        }

    def __init__(self, *args, **kwargs):
        super(ProductoForm, self).__init__(*args, **kwargs)
        self.fields['stock'].widget.attrs['readonly'] = True
        
class UpdateProductoForm(forms.ModelForm):

    class Meta:
        model = Producto
        fields = ['codigo','nombre', 'descripcion', 'stock',
                  'precio_compra','precio_venta','ingreso']

        widgets = {
            'codigo': forms.TextInput(attrs={'class': 'form-control','placeholder':'  Codigo','autofocus': True,'require':True,'style':'border: 1px solid; color:black;'}),
            'nombre': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Nombre de Producto', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'descripcion': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Descripcion de Producto', 'style': 'color: black', 'require': False}),
            'stock': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_compra': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
            'precio_venta': forms.TextInput(attrs={'name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}), 'ingreso': forms.TextInput(attrs={'class': 'form-control','placeholder':'  Ingreso','require':True,'style':'border: 1px solid; color:black;'}),
        }

    def __init__(self, *args, **kwargs):
        super(ProductoForm, self).__init__(*args, **kwargs)
        self.fields['stock'].widget.attrs['readonly'] = True


class ProductoFacturaForm(forms.ModelForm):

    class Meta:
        model = ProductoFactura
        fields = ['factura', 'serie', 'cantidad',
                  'total', 'fecha_factura']

        widgets = {
            'factura': forms.TextInput(attrs={'name': 'nombre', 'class': 'form-control', 'placeholder': 'Numero de Factura', 'style': 'color: black', 'require': True, 'autofocus': True}),
            'serie': forms.TextInput(attrs={'name': 'descripcion', 'class': 'form-control', 'placeholder': 'Serie de Factura', 'style': 'color: black', 'require': False}),
            'cantidad': forms.TextInput(attrs={'name': 'stock', 'class': 'form-control', 'placeholder': 'Cantidad de Arituclos en Factura', 'style': 'color: black', 'require': True}),
            'total': forms.TextInput(attrs={'name': 'precio_compra', 'class': 'form-control', 'placeholder': 'Total', 'style': 'color: black', 'require': True}),
            'fecha_factura': forms.TextInput(attrs={'type':'date','name': 'precio_venta', 'class': 'form-control', 'placeholder': '0', 'style': 'color: black', 'require': True}),
        }