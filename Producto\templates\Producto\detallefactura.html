{% extends 'BaseInicio/base.html' %}
{% block title %}Ingreso de Productos Por Factura{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}


<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width:90%;"><br>

  <div class="container-xxl flex-grow-1 container-p-y">

    <div class="card-body" style="background-color: white;">

      <div class="row">

        <div class="col-md-2">
          <label for="">Factura</label>
          <p style="color: red;">{{c.factura}}</p>
        </div>

        <div class="col-md-3">
          <label for="">Proveedor</label>
          <p style="color: red;">{{c.id_prov.nombre}}</p>
        </div>

        <div class="col-md-2">
          <label for="">Items</label>
          <p style="color: red;">{{c.cantidad}}
            {% if i == c.cantidad %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Total</label>
          <p style="color: red;">Q.{{c.total}}
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Fecha</label>
          <p style="color: red;">{{c.fecha|date:"d-m-Y"}}</p>
        </div>

      </div><br><br>

      <div class="row">

        <div class="col-md-2">
          <label for="">Articulos Ingresados</label>
          {% if i == None %}
          <p style="color: red;">0
            {% else %}
          <p style="color: red;">{{i}}
            {% endif %}
            {% if i == c.cantidad %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

        <div class="col-md-2">
          <label for="">Total Ingresado</label>
          {% if total == None %}
          <p style="color: red;">Q.0.00
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
          {% else %}
          <p style="color: red;">Q.{{total}}
            {% if total == c.total %}
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
          {% endif %}
        </div>

        <div class="col-md-2">
          <label for="">Estado Ingreso</label>
          {% if total == c.total and i == c.cantidad %}
          <p style="color: green;">INGRESO CUADRADO
            <i style="color:green" class='bx bxs-check-shield'></i>
            {% else %}
            <i style="color:red" class='bx bx-error'></i>
            {% endif %}
          </p>
        </div>

      </div><br>


      <hr>
      <form action="#" method="POST">{% csrf_token %}
        <div class="row" style="border-bottom: 2px solid black;" align="center">


          <div class="col-md-3">

            <label for="">Tipo Busqueda</label>
            <select name="tipo" class="form-control" required>
              <option value="nombre">Nombre Producto</option>
              <option value="codigo">Codigo Producto</option>
            </select>
          </div>

          <div class="col-md-6">
            <label for="">Busqueda de Productos</label><br>
            <input type="text" name="buscar" class="form-control" autofocus="buscar" placeholder="Agregar/Buscar">
      
          </form><br>
    </div>


 


    <div class="col-md-12" style="margin-top: 15px;">
      <form action="#" method="POST">{% csrf_token %}

        <div class="row mb-3">
          <div class="col-md-12">
              <label for="nota">Nota:</label>
              <textarea id="nota" name="nota" class="form-control" rows="2" placeholder="Añadir una nota...">{{ c.nota|default:'' }}</textarea>
          </div>
      </div>


          <div class="row mb-10" style="margin-left: 15px;">
              <div class="col-md-4 d-flex align-items-center">
                <label for="credito"><h4>Compra a Crédito:</h4></label>
                  <input type="checkbox" name="credito" id="credito" 
                        style="transform: scale(2); -webkit-transform: scale(2); margin-left: 10px;"
                         {% if c.estado == 3 %}checked{% endif %}>
              </div>
              <button name="terminar" class="btn btn-danger">Terminar</button>
          </div><br>
      </form>
  </div>
  

  </div>

  <div class="row">

    <div class="col-md-12">

      {% if b %}
      <!-- Modal para la tabla de productos encontrados -->
      <div class="modal fade" id="tablaProductosModal" tabindex="-1" role="dialog" aria-labelledby="tablaProductosModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tablaProductosModalLabel">Productos Encontrados</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Tabla responsiva -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Codigo</th>
                                    <th>Producto</th>
                                    <th>Stock</th>
                                    <th>Precio Compra</th>
                                    <th>Precio Venta</th>
                                    <th>Nuevo Compra</th>
                                    <th>Nuevo Venta</th>
                                    <th>Cant</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for p in buscar %}
                                <tr>
                                    <td>{{p.id}}</td>
                                    <td>{{p.codigo}}</td>
                                    <td>{{p.nombre}}</td>
                                    <td>{{p.stock}}</td>
                                    <td>Q.{{p.precio_compra}}</td>
                                    <td>Q.{{p.precio_venta}}</td>
                                    <form action="#" method="POST">
                                        {% csrf_token %}
                                        <input type="hidden" value="{{p.id}}" name="id">
                                        <td><input type="text" name="nuevocompra" placeholder="0"></td>
                                        <td><input type="text" name="nuevoventa" placeholder="0"></td>
                                        <td><input type="text" name="cantidad" placeholder="0"></td>
                                        <td>
                                            <button name="agregar" class="btn btn-sm btn-success">
                                                <i style="color: white;" class="bx bx-plus-circle">+</i>
                                            </button>
                                        </td>
                                    </form>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
      <script>
        $(document).ready(function() {
          $('#tablaProductosModal').modal('show');
        });
      </script>
      {% endif %}
    </div>
  </div>
      
      {% if h %}
       <!-- Modal para crear un nuevo producto -->
       <div class="modal fade" id="crearProductoModal" tabindex="-1" role="dialog" aria-labelledby="crearProductoModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="crearProductoModalLabel">Producto No Existe</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <form action="#" method="POST">
                {% csrf_token %}
                <div class="form-group">
                  <label for="nuevoNombreProducto">Nombre del Producto</label>
                  <input type="text" class="form-control" id="nuevoNombreProducto" name="nuevo_nombre" value="{% if tipo == 'nombre' %}{{ query }}{% endif %}" placeholder="Nombre del Nuevo Producto" required>
                </div>
                <div class="form-group">
                  <label for="nuevoCodigoProducto">Código del Producto</label>
                  <input type="text" class="form-control" id="nuevoCodigoProducto" name="nuevo_codigo" value="{% if tipo == 'codigo' %}{{ query }}{% endif %}" placeholder="Código del Nuevo Producto" required>
                </div>
                <div class="form-group">
                  <label for="categoriaProducto">Categoría</label>
                  <select class="form-control" id="categoriaProducto" name="cate">
                    {% for c in cate %}
                    <option value="{{ c.id }}">{{ c.nombre }}</option>
                    {% endfor %}
                  </select>
                </div>
                <button type="submit" name="crear_producto" class="btn btn-success">Crear Producto</button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <script>
        $(document).ready(function() {
          $('#crearProductoModal').modal('show');
        });
      </script>
      {% endif %}
      

    </div>

  </div><br>



  <div class="row" align="center">

    <div class="col-md-12">

      <label for="">Productos Agregados</label>

      <div style="height: 12rem; overflow-y: scroll;">
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th scope="col">Cod</th>
                <th scope="col">Prod</th>
                <th scope="col">Cant</th>
                <th scope="col">Precio Compra</th>
                <th scope="col">Precio Venta</th>
                <th scope="col">Total</th>
              </tr>
            </thead>
            <tbody>
              {% for d in d %}
              <tr>
                <th scope="row">{{d.id_prod.id}}</th>
                <td>{{d.id_prod.nombre}}</td>
                <td>{{d.cantidad}}</td>
                <td>Q{{d.compra_ahora}}</td>
                <td>Q{{d.venta_ahora}}</td>
                <td>Q.{{d.total}}</td>
                <form action="#" method="POST">{% csrf_token %}
                  <input type="hidden" name="corr" value="{{d.id}}">
                  <td><button name="quitar" class="btn btn-sm btn-danger">Eliminar<i style="color: white;"
                        class="bx bx-x"></i></button>
                  </td>
                </form>
              </tr>
              {% empty %}
              <caption>SIN PRODUCTOS</caption>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

    </div>

  </div>



</div>

</div>


<script>
  document.addEventListener('DOMContentLoaded', function () {
      // Para los botones que abren modales
      $('[data-toggle="modal"]').on('click', function () {
          var targetModal = $($(this).data('target'));
          targetModal.modal('show');
      });

      // Mostrar automáticamente el modal de búsqueda si 'b' es verdadero
      {% if b %}
      $('#tablaProductosModal').modal('show');
      {% endif %}

      // Mostrar automáticamente el modal de creación si 'h' es verdadero
      {% if h %}
      $('#crearProductoModal').modal('show');
      {% endif %}

      // Cerrar el modal al hacer clic en "Agregar"
      $('form button[name="agregar"]').on('click', function () {
          $('#tablaProductosModal').modal('hide');
      });
  });
</script>

<style>
  /* Ajusta el tamaño del modal */
  @media (min-width: 992px) {
      .modal-xl {
          max-width: 90vw; /* Hace el modal más ancho */
          max-height: 90vh; /* Limita la altura máxima */
      }
  }

  /* Flexibilidad en el modal */
  .modal-content {
      display: flex;
      flex-direction: column;
      height: 90vh; /* Define la altura total */
  }

  .modal-body {
      flex-grow: 1; /* Hace que la tabla ocupe el espacio disponible */
      overflow-y: auto; /* Permite desplazamiento vertical */
      overflow-x: hidden; /* Evita desplazamiento horizontal */
  }

  /* Asegura que la tabla sea fluida */
  .table-responsive {
      height: 100%; /* Ocupa todo el espacio disponible */
  }

  /* Personaliza la barra de scroll */
  .modal-body::-webkit-scrollbar {
      width: 8px;
  }

  .modal-body::-webkit-scrollbar-track {
      background: #f1f1f1;
  }

  .modal-body::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
  }

  .modal-body::-webkit-scrollbar-thumb:hover {
      background: #555;
  }
</style>



{% endblock %}