{% extends 'BaseInicio/base.html' %}

{% block content %}
<div class="container mt-4" style="background-color: #f9f9f9; padding: 20px; border-radius: 8px; max-width: 90%;">
    <div class="mb-3">
        <a href="{% url 'Listado' %}" class="btn btn-warning">Regresar</a>
    </div>

    <h3 class="text-center text-primary mb-4">Formulario de Ingreso de Planilla</h3>

    <div class="mb-4">
        <form id="busquedaEmpleadoForm" method="post" action="{% url 'NuevaPlanilla' %}" class="d-flex flex-column align-items-center">
            {% csrf_token %}
            <div class="mb-3" style="width: 100%; max-width: 350px;">
                <label for="buscar_empleado" class="form-label">Buscar por Nombre o DPI:</label>
                <input type="text" name="buscar_empleado" id="buscar_empleado" class="form-control" 
                       style="border: 1px solid #007bff; border-radius: 5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);" 
                       placeholder="Ingrese nombre o DPI" required>
            </div>
            <button type="button" class="btn btn-primary" 
                    style="height: 40px; padding: 0 15px; border-radius: 5px; font-weight: bold;" 
                    onclick="buscarEmpleado()">Buscar</button>
        </form>
    </div>

    <!-- Campos llenados automáticamente -->
    <div class="row mt-3">
        <div class="col-md-4 mb-3">
            <label for="nombre" class="form-label">Nombre del Empleado</label>
            <input type="text" id="nombre" name="nombre" class="form-control" readonly>
        </div>
        <div class="col-md-4 mb-3">
            <label for="apellido" class="form-label">Apellido del Empleado</label>
            <input type="text" id="apellido" name="apellido" class="form-control" readonly>
        </div>
    
        <div class="col-md-4 mb-3">
            <label for="sueldo" class="form-label">Sueldo</label>
            <input type="text" id="sueldo" name="sueldo" class="form-control" readonly>
        </div>
    </div>

        <div class="col-md-4">
   
        <div class="col-md-4 mb-3">
            <label for="bonificacion" class="form-label">Bonificación</label>
            <input type="number" step="0.01" id="bonificacion" name="bonificacion" class="form-control" required>
        </div>
    </div>

    <!-- Campos adicionales -->
    <div class="row mb-3">
        <div class="col-md-4 mb-3">
            <label for="otras_deducciones" class="form-label">Otras Deducciones</label>
            <input type="number" step="0.01" id="otras_deducciones" name="otras_deducciones" class="form-control" required>
        </div>
        <div class="col-md-4 mb-3">
            <label for="horas_extras" class="form-label">Horas Extras</label>
            <input type="number" step="0.01" id="horas_extras" name="horas_extras" class="form-control" required>
        </div>
        <div class="col-md-4 mb-3">
            <label for="liquido" class="form-label">Sueldo Líquido</label>
            <input type="number" step="0.01" id="liquido" name="liquido" class="form-control" readonly>
        </div>
    </div>

    <div class="text-center">
        <button type="submit" class="btn btn-success">Crear Planilla</button>
    </div>
</div>

<div class="mt-4">
    {% if messages %}
        <div class="alert alert-info">
            {% for message in messages %}
                <p>{{ message }}</p>
            {% endfor %}
        </div>
    {% endif %}
</div>

<script>
    function buscarEmpleado() {
        const buscarEmpleado = document.getElementById("buscar_empleado").value;
    
        fetch(`/empleado/nuevaplanilla/?buscar_empleado=${buscarEmpleado}`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest"
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                document.getElementById("nombre").value = data.nombre;
                document.getElementById("apellido").value = data.apellido; 
                document.getElementById("sueldo").value = data.sueldo;
                calcularLiquido();
            } else {
                alert("Empleado no encontrado");
            }
        })
        .catch(error => console.error("Error:", error));
    }

    document.getElementById("bonificacion").addEventListener("input", calcularLiquido);
    document.getElementById("otras_deducciones").addEventListener("input", calcularLiquido);
    document.getElementById("horas_extras").addEventListener("input", calcularLiquido);

    function calcularLiquido() {
        const sueldo = parseFloat(document.getElementById("sueldo").value) || 0;
        const bonificacion = parseFloat(document.getElementById("bonificacion").value) || 0;
        const otrasDeducciones = parseFloat(document.getElementById("otras_deducciones").value) || 0;
        const horasExtras = parseFloat(document.getElementById("horas_extras").value) || 0;

        const liquido = (sueldo - otrasDeducciones + bonificacion + horasExtras);
        document.getElementById("liquido").value = liquido.toFixed(2);
    }
</script>
{% endblock %}
