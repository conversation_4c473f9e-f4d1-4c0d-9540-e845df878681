<div class="modal-body">
    <h5 class="modal-title">Factura #{{ factura.factura }}</h5>
    <hr>

    <div class="mb-3">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Serie:</strong> <span class="text-muted">{{ factura.serie }}</span></p>
            </div>
            <div class="col-md-6">
                <p><strong><PERSON><PERSON> de Factura:</strong> <span class="text-muted">{{ factura.fecha_factura }}</span></p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Total:</strong> <span class="text-muted">{{ factura.total }}</span></p>
            </div>
            <div class="col-md-6">
                <p><strong>Proveedor:</strong> <span class="text-muted">{{ factura.id_prov }}</span></p>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nota:</strong> <span class="text-muted">{{ factura.nota }}</span></p>
            </div>
            
        </div>
    </div>
    <h6 class="mt-4">Productos:</h6>
    <div class="table-responsive">
        <table class="table table-sm table-striped">
            <thead>
                <tr>
                    <th>Producto</th>
                    <th>Cantidad</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                {% for detalle in detalle_factura %}
                <tr>
                    <td>{{ detalle.id_prod }}</td>
                    <td>{{ detalle.cantidad }}</td>
                    <td>{{ detalle.total }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="3" class="text-center">No hay productos en esta factura.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
