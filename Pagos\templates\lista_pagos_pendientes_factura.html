{% extends 'BaseInicio/base.html' %}
{% block content %}

<br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>
    <a href="{% url 'ventas_y_facturas' %}" class="btn btn-warning">Listado de Facturas </a>
    <br></br> 

    <h2>Facturas al Credito</h2>
    <form method="get" class="form-inline mb-4" style="max-width: 500px; margin: 0 auto;">
    <input type="text" name="buscar" class="form-control form-control-lg mr-2 w-75" placeholder="Buscar por empresa, factura o serie..." value="{{ buscar }}">
    <button type="submit" class="btn btn-primary btn-lg">Buscar</button>
    </form>

    <table class="table table-striped mt-4">
        <thead>
            <tr>
                <th>Empresa</th>
                <th>Factura</th>
                <th>Serie</th>
                <th>Fecha Factura</th>
                <th>Total</th>
                <th>Abono Total</th>
                <th>Saldo</th>
                <th>Abonar</th>
            </tr>
        </thead>
        <tbody>
            {% for item in facturas_con_abonos %}
            <tr>
                <td>{{ item.factura.id_prov.nombre }}</td>
                <td>{{ item.factura.factura }}</td>
                <td>{{ item.factura.serie }}</td>
                <td>{{ item.factura.fecha_factura }}</td>
                <td>Q{{ item.factura.total }}</td>
                <td>Q{{ item.abono_total }}</td>
                <td>Q{{ item.queda }}</td>
                <td>
                    <a href="{% url 'NuevoPagoFacturaCredito' item.factura.factura %}" class="btn btn-info">Abonar</a>
                    <a href="{% url 'generar_pdf_factura_credito' item.factura.factura %}" class="btn btn-secondary">Descargar PDF</a>
                    

                </td>
            </tr>
            {% empty %}
<tr>
    <td colspan="7">
        {% if buscar %}
            No se encontraron resultados para "{{ buscar }}".
        {% else %}
            No hay facturas pendientes de pago.
        {% endif %}
    </td>
</tr>

            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
