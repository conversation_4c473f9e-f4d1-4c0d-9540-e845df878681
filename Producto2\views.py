from django.shortcuts import render, redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Producto.models import Producto
from Producto.forms import ProductoForm, UpdateProductoForm
from user.models import User
from django.http import HttpResponse


@login_required
def nuevo(request):
    form = ProductoForm()
    if request.method == "POST":
        form = ProductoForm(request.POST)
        if form.is_valid():
            p = Producto()
            p.codigo = form.cleaned_data['codigo']
            p.nombre = form.cleaned_data['nombre']
            p.descripcion = form.cleaned_data['descripcion']
            p.stock = form.cleaned_data['ingreso']
            p.ingreso = form.cleaned_data['ingreso']
            p.precio_compra = form.cleaned_data['precio_compra']
            p.precio_venta = form.cleaned_data['precio_venta']
            p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
            p.usuario = User.objects.get(id=request.user.id)
            p.save()
            messages.success(request, f'Producto {p.nombre} Ingresado!')
            return redirect('NuevoProducto')

    return render(request, 'Producto/nuevo.html', {'form': form})


@login_required
def listado(request):
    datos = Producto.objects.all()
    return render(request, 'Producto/lista.html', {'prod': datos})



@login_required
def eliminar(request, id):
    try:
        prod = Producto.objects.get(id=id)
        prod.delete()
        messages.success(request, f'{prod.nombre} Eliminado!')
        return redirect('ListaProducto')
    except:
        messages.error(request, f'No Se Puede Eliminar {prod.nombre}')
        return redirect('ListaProducto')




        
        
        
        
        
           