{% extends 'BaseInicio/base.html' %}
{% block content %}
<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 80%;"><br>

    <h2>Abono a la Factura: {{ factura.factura }}</h2>
    <p><strong>Total Factura:</strong> Q{{ factura.total }}</p>
    <p><strong>Saldo Pendiente:</strong> Q{{ queda }}</p>
    
    <form method="POST" action="">
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit" class="btn btn-success">Realizar Abono</button>
    </form>

    <h3 class="mt-4">Historial de Abonos</h3>
    <table class="table table-striped mt-2">
        <thead>
            <tr>
                <th>Fecha</th>
                <th>Tipo</th>
                <th>Monto</th>
                <th>Usuario</th>
            </tr>
        </thead>
        <tbody>
            {% for abono in abonos_realizados %}
            <tr>
                <td>{{ abono.fecha }}</td>
                <td>{{ abono.tipo }}</td>
                <td>Q{{ abono.abono }}</td>
                <td>{{ abono.usuario.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
