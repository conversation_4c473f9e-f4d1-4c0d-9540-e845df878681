from decimal import Decimal
from django.shortcuts import render, redirect,get_object_or_404
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Pagos.models import PagoFacturaCredito
from Proveedor.models import Proveedor
from Producto.models import Producto, ProductoFactura, DetalleFactura, Bitacora
from Producto.forms import ProductoForm, UpdateProductoForm, ProductoFacturaForm
from user.models import User
#import xlwt
from django.http import HttpResponse
from django.db.models import Q
from django.db.models import Sum
from Categoria.models import Categoria
from django.views.generic import ListView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.http import JsonResponse

@login_required
def nuevo(request):
    p = Proveedor.objects.all()
    form = ProductoFacturaForm()
    if request.method == "POST":
        if request.user.rol == "admin":
                form = ProductoFacturaForm(request.POST)
                if form.is_valid():
                    p = ProductoFactura()
                    p.factura = form.cleaned_data['factura']
                    p.serie = form.cleaned_data['serie']
                    p.fecha_factura = form.cleaned_data['fecha_factura']
                    p.cantidad = form.cleaned_data['cantidad']
                    p.total = form.cleaned_data['total']
                    p.id_prov = Proveedor.objects.get(
                        nit=request.POST['id_prov'])
                    p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                    p.usuario = User.objects.get(id=request.user.id)
                    p.estado = 1
                    p.save()
                    messages.success(
                        request, f'Factura {p.factura} Ingresada!')
                    return redirect('DetalleIngresoFactura', p.factura)
        else:
            form = ProductoFacturaForm(request.POST)
            if form.is_valid():
                p = ProductoFactura()
                p.factura = form.cleaned_data['factura']
                p.serie = form.cleaned_data['serie']
                p.fecha_factura = form.cleaned_data['fecha_factura']
                p.cantidad = form.cleaned_data['cantidad']
                p.total = form.cleaned_data['total']
                p.id_prov = Proveedor.objects.get(nit=request.POST['id_prov'])
                p.fecha = str(datetime.today().strftime('%Y-%m-%d'))
                p.usuario = User.objects.get(id=request.user.id)
                p.estado = 1
                p.save()
                messages.success(request, f'Factura {p.factura} Ingresada!')
                return redirect('DetalleIngresoFactura', p.factura)

    return render(request, 'Producto/nuevofactura.html', {'form': form, 'c': p})



from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db.models import Sum
from decimal import Decimal
from datetime import datetime
from .models import Producto, DetalleFactura, ProductoFactura, Categoria

@login_required
def detalle(request, f):
    h = False
    cate = Categoria.objects.all()
    factura = ProductoFactura.objects.filter(factura=f).first()

    if not factura:
        messages.error(request, '¡Está intentando ingresar productos sin una factura previa!')
        return redirect('IngresoFactura')

    detalles = DetalleFactura.objects.filter(factura=f)
    activos = detalles.filter(estado=1)
    items = activos.aggregate(it=Sum('cantidad'))
    tot = activos.aggregate(t=Sum('total'))

    if request.method == 'POST':
        if 'buscar' in request.POST:
            query = request.POST['buscar'].strip()
            tipo = request.POST['tipo']

            if tipo == 'nombre':
                buscar = Producto.objects.filter(nombre__icontains=query).order_by('nombre')
            else:
                buscar = Producto.objects.filter(codigo=query).order_by('codigo')

            b = buscar.exists()
            h = not b

            return render(request, 'Producto/detallefactura.html', {
                'f': f, 'c': factura, 'd': detalles, 'b': b, 'buscar': buscar,
                'i': items['it'], 'total': tot['t'], 'h': h, 'cate': cate,
                'query': query, 'tipo': tipo
            })

        elif 'crear_producto' in request.POST:
            nombre = request.POST.get('nuevo_nombre', '').strip()
            codigo = request.POST.get('nuevo_codigo', '').strip()

            if not nombre or not codigo:
                messages.error(request, '¡Debe proporcionar tanto el nombre como el código del producto!')
                return redirect('DetalleIngresoFactura', f)

            try:
                categoria = Categoria.objects.get(id=request.POST['cate'])
                nuevo_producto = Producto(
                    nombre=nombre,
                    codigo=codigo,
                    stock=0,
                    precio_compra=Decimal(0.00),
                    precio_venta=Decimal(0.00),
                    id_cate=categoria,
                    fecha=datetime.today().strftime('%Y-%m-%d'),
                    usuario=request.user
                )
                nuevo_producto.save()
                messages.success(request, f'¡Producto {nuevo_producto.codigo} - {nuevo_producto.nombre} creado e ingresado exitosamente!')
            except Exception as e:
                messages.error(request, f'Error al crear el producto: {e}')
            return redirect('DetalleIngresoFactura', f)

        elif 'agregar' in request.POST:
            try:
                prod = get_object_or_404(Producto, id=request.POST['id'])
                cantidad = int(request.POST['cantidad'])
                nueva_compra = Decimal(request.POST['nuevocompra']) if request.POST['nuevocompra'] else prod.precio_compra
                nueva_venta = Decimal(request.POST['nuevoventa']) if request.POST['nuevoventa'] else prod.precio_venta

                detalle = DetalleFactura(
                    factura=factura,
                    id_prod=prod,
                    cantidad=cantidad,
                    compra_antes=prod.precio_compra,
                    compra_ahora=nueva_compra,
                    venta_antes=prod.precio_venta,
                    venta_ahora=nueva_venta,
                    stock_antes=prod.stock,
                    stock_ahora=prod.stock + cantidad,
                    total=cantidad * nueva_compra,
                    fecha=datetime.today(),
                    usuario=request.user,
                    estado=1
                )
                detalle.save()

                prod.stock += cantidad
                prod.ingreso += cantidad
                prod.precio_compra = nueva_compra
                prod.precio_venta = nueva_venta
                prod.save()

                messages.success(request, f'¡Se ingresaron {cantidad} unidades del producto {prod.nombre}!')
            except Exception as e:
                messages.error(request, f'Error al ingresar el producto. ¡Revise los datos! Error: {e}')
            return redirect('DetalleIngresoFactura', f)

        elif 'quitar' in request.POST:
            try:
                datos = get_object_or_404(DetalleFactura, id=request.POST['corr'], factura=f)
                prod = datos.id_prod

                prod.stock -= datos.cantidad
                prod.ingreso -= datos.cantidad
                prod.precio_compra = datos.compra_antes
                prod.precio_venta = datos.venta_antes
                prod.save()

                datos.delete()
                messages.success(request, f'¡Se quitaron {datos.cantidad} unidades del producto {prod.nombre}! Inventario actualizado.')
            except Exception as e:
                messages.error(request, f'¡Error al quitar el producto! {e}')
            return redirect('DetalleIngresoFactura', f)

        elif 'terminar' in request.POST:
            factura.estado = 3 if 'credito' in request.POST else 1
            factura.nota = request.POST.get('nota', '')
            factura.save()
            messages.success(request, 'Factura actualizada correctamente')
            return redirect('IngresoFactura')

    return render(request, 'Producto/detallefactura.html', {
        'f': f, 'c': factura, 'd': detalles, 'i': items['it'],
        'total': tot['t'], 'h': h, 'cate': cate,
        'b': None, 'buscar': None, 'query': '', 'tipo': ''
    })


@login_required
def lista(request):
    # Obtiene el texto de búsqueda (si existe)
    consulta = request.GET.get('q', '')

    # Filtra las facturas que están en estado 1 (Pagada)
    facturas_pagadas = ProductoFactura.objects.filter(estado=1)

    # Si hay búsqueda, filtra por número de factura o nombre del proveedor
    if consulta:
        facturas_pagadas = facturas_pagadas.filter(
            Q(factura__icontains=consulta) |
            Q(id_prov__nombre__icontains=consulta)
        )

    # Filtra los pagos relacionados con las facturas pagadas
    pagos = PagoFacturaCredito.objects.filter(factura__in=facturas_pagadas)

    datos = {
        'facturas_pagadas': facturas_pagadas,
        'pagos': pagos
    }
    return render(request, 'Producto/facturas_pagadas.html', {'datos': datos})


@login_required
def darbaja(request, f):

    if ProductoFactura.objects.filter(factura=f).exists():
        for d in DetalleFactura.objects.filter(factura=f, estado=1):
            prod = Producto.objects.get(id=d.id_prod.id)
            Producto.objects.filter(id=prod.id).update(stock=prod.stock-d.cantidad, ingreso=prod.ingreso -
                                                       d.cantidad, precio_compra=d.compra_antes, precio_venta=d.venta_antes)
            DetalleFactura.objects.filter(
                factura=f, estado=1).update(estado=99)

        ProductoFactura.objects.filter(factura=f, estado=1).update(estado=99)
        messages.success(
            request, f'Productos de Factura # {f} Fueron Dados de Baja Inventario de Estos Productos Vuelve a Precios y Stock Anterior!')
        return redirect('ListaIngresoFactura')

    else:
        messages.error(
            request, f'Error Al Dar de Baja Factura de Ingreso # {f}!')
        return redirect('ListaIngresoFactura')



def bitacora(id, id_pr, prod, t, d, h, i, s, hy, td, u):

    b = Bitacora()
    b.id_prod = id_pr
    b.prod = prod
    b.tipo = t
    b.doc = id
    b.habia = h
    b.ingreso = i
    b.salio = s
    b.hay = hy
    b.fecha = str(datetime.today())
    b.usuario = u
    b.save()   
    
    
    
    
    
    
    
class FacturaListView(ListView):
    model = ProductoFactura
    template_name = 'Producto/factura_list.html'
    context_object_name = 'facturas'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

class FacturaDeleteView(DeleteView):
    model = ProductoFactura
    success_url = reverse_lazy('factura_list')

    def post(self, request, *args, **kwargs):
        factura = self.get_object()
        if not DetalleFactura.objects.filter(factura=factura).exists():
            factura.delete()
            return JsonResponse({'deleted': True})
        return JsonResponse({'deleted': False, 'error': 'Cannot delete, products associated.'})

class FacturaDetailView(DetailView):
    model = ProductoFactura
    template_name = 'Producto/factura_detail_modal.html'
    context_object_name = 'factura'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['detalle_factura'] = DetalleFactura.objects.filter(factura=self.object)
        return context