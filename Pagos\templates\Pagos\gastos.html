{% extends 'BaseInicio/base.html' %}
{% block title %}Gastos del Dia{% endblock %}
{% block carta %}<i class="fa-solid fa-house"></i>Gastos del Dia{% endblock %}
{% load static %}
{% block content %}

<div class="container">

    <a href="{% url 'TodoGasto' %}" class="btn btn-info">Lista de Pagos</a>
  
  </div>
  <br>
<div class="container container-venta">
    <label for="DatosCliente" class="form-label label-venta">
        <h4>GASTOS DEL DIA</h4>
    </label>

    {{form.errors}}

    <form action="#" method="POST">{% csrf_token %}
        <div class="row col-md">
            <div class="col-md-4">
                <label for="Nombre" class="form-label">Nombre:</label>
                {{form.nombre}}
            </div>
            <div class="col-md-4">
                <label for="Cantidad" class="form-label">Cantidad:</label>
                {{form.cantidad}}
            </div>
            <div class="col-md-4">
                <label for="" class="form-label">Precio Unitario:</label>
                {{form.precio_uni}}
            </div>

            <div class="col-md-4">
                <label class="form-label">Fecha de Incio</label>
                <div class="col-md-12">
                    <input type="date" class="form-control" name="fecha_pago" required>
                </div>
            </div>
        </div>

        <hr >
        <div class="col-md" style="text-align: center;">
            <br></br>
            <button class="btn btn-success" type="submit">Guardar</button>
            <a href="#"><button class="btn btn-danger" type="reset">Cancelar</button></a>
        </div>

        <br />
    </form>



    <div class="col-md-6">
        {% if messages %}
        {% for message in messages %}

        <script>
            Swal.fire({
                "title": "Informacion Sistema",
                "text": "{{message}}",
                "icon": "{{message.tags}}"
            })
        </script>

        {% endfor %}
        {% endif %}
    </div>

</div>

{% endblock %}