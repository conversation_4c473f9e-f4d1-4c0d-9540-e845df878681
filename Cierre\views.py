from datetime import date, datetime, time
from decimal import Decimal
from django.shortcuts import render, redirect
from django.utils import timezone
from django.db.models import Sum, Q
from django.contrib import messages

from CajaChica.models import CajaChica
from Cierre.models import CierreCaja
from Creditos.models import Pago
from Pagos.models import PagoFacturaCredito, Gasto as GastoPagos
from Producto.models import ProductoFactura
from Venta.models import  Venta, PagoCreditoFel
from django.db.models.functions import TruncDate



def cierre_dia(request):
    hoy = timezone.localtime(timezone.now()).date()
    cierre_hecho = CierreCaja.objects.filter(fecha_cierre=hoy).exists()

    hoy_start = timezone.make_aware(datetime.combine(hoy, time.min))
    hoy_end = timezone.make_aware(datetime.combine(hoy, time.max))

    # Caja chica del día
    total_caja_chica = CajaChica.objects.filter(fecha_creacion=hoy).aggregate(total=Sum('cantidad'))['total'] or 0

    # Ventas al contado (excluyendo cotizaciones y CreditoFel)
    total_ventas = Venta.objects.filter(
        fecha__range=(hoy_start, hoy_end),
        estado=Venta.ESTADO_PAGADA
    ).exclude(tipo__iexact='cotizacion').exclude(tipo='CreditoFel').aggregate(total=Sum('total'))['total'] or 0

    # Cotizaciones del día
    cotizaciones_dia = Venta.objects.filter(
        fecha__range=(hoy_start, hoy_end),
        tipo__iexact='cotizacion'
    ).aggregate(total=Sum('total'))['total'] or 0

    # Ventas a crédito (estado = 3, excluyendo CreditoFel)
    ventas_credito_neto = Venta.objects.filter(
        fecha__range=(hoy_start, hoy_end),
        estado=3
    ).exclude(tipo='CreditoFel').aggregate(total=Sum('total'))['total'] or 0

    # Créditos FEL pendientes (estado = 3, tipo = CreditoFel)
    creditos_fel_pendientes = Venta.objects.filter(
        fecha__range=(hoy_start, hoy_end),
        estado=3,
        tipo='CreditoFel'
    ).aggregate(total=Sum('total'))['total'] or 0

    # Pagos de créditos FEL realizados hoy
    pagos_creditos_fel = PagoCreditoFel.objects.filter(
        fecha_pago=hoy
    ).aggregate(total=Sum('monto_pagado'))['total'] or 0

    # Abonos a créditos (clientes pagando su deuda)
    total_abonos = Pago.objects.filter(fecha=hoy).aggregate(total=Sum('abono'))['total'] or 0

    # Gastos pagados hoy
    total_pagos = GastoPagos.objects.filter(fecha_pago=hoy).aggregate(total=Sum('total'))['total'] or 0

    # Pagos a facturas crédito hechos hoy
    total_facturas_credito = PagoFacturaCredito.objects.filter(fecha=hoy).aggregate(total=Sum('abono'))['total'] or 0

    # Facturas a crédito generadas hoy (no se pagan hoy, solo se registran)
    facturas_credito_dia = ProductoFactura.objects.filter(
        fecha_factura=hoy,
        estado=3  # Asegúrate que 3 representa 'a crédito'
    ).aggregate(total=Sum('total'))['total'] or 0

    # Totales
    total_ingresos = total_caja_chica + total_ventas + total_abonos + pagos_creditos_fel
    total_salidas = total_pagos + total_facturas_credito
    balance = total_ingresos - total_salidas

    if request.method == 'POST' and not cierre_hecho:
        CierreCaja.objects.create(
            total_caja_chica=total_caja_chica,
            total_abonos=total_abonos,
            total_ventas=total_ventas,
            total_pagos=total_pagos,
            total_facturas_credito=total_facturas_credito,
            pagos_creditos_fel=pagos_creditos_fel,
            total_ingresos=total_ingresos,
            total_salidas=total_salidas,
            balance=balance,
            fecha_cierre=hoy
        )
        messages.success(request, 'Cierre del día registrado exitosamente.')
        return redirect('cierre_dia')  # Debe coincidir con el name de la url

    context = {
        'hoy': hoy,
        'cierre_hecho': cierre_hecho,
        'total_caja_chica': total_caja_chica,
        'total_abonos': total_abonos,
        'total_ventas': total_ventas,
        'total_pagos': total_pagos,
        'total_facturas_credito': total_facturas_credito,
        'total_ingresos': total_ingresos,
        'total_salidas': total_salidas,
        'balance': balance,
        'cotizaciones_dia': cotizaciones_dia,
        'ventas_credito_neto': ventas_credito_neto,
        'facturas_credito_dia': facturas_credito_dia,
        'creditos_fel_pendientes': creditos_fel_pendientes,
        'pagos_creditos_fel': pagos_creditos_fel,
    }

    return render(request, 'cierre_dia.html', context)




def listado_cierres(request):
    cierres = CierreCaja.objects.order_by('-fecha_cierre')  # más reciente primero
    context = {
        'cierres': cierres,
    }
    return render(request, 'listado_cierres.html', context)



