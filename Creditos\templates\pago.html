{% extends 'BaseInicio/base.html' %}

{% block content %}
<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>

    <h2>Abono a la Factura: {{ venta.factura }}</h2>
    <p><strong>Total Venta:</strong> Q{{ venta.total }}</p>
    <p><strong>Saldo Pendiente:</strong> Q{{ queda }}</p>

    <form method="POST" action="" id="form-pago">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="id_tipo">Tipo</label>
            {{ form.tipo }}
        </div>
        
        <div class="form-group">
            <label for="id_abono">Abono</label>
            {{ form.abono }}
        </div>

        <!-- Campo de descuento mostrado dinamicamente -->
        <div id="descuento-container" style="display: none;">
            <label for="id_descuento_porcentaje">Descuento (%)</label>
            {{ form.descuento_porcentaje }}
            <br>
            <button type="button" class="btn btn-info" onclick="calcularDescuento()">Calcular Descuento</button>
        </div>

        <p><strong>Total a Pagar:</strong> Q<span id="nuevo_total">{{ queda }}</span></p>

        <button type="submit" class="btn btn-success">Realizar Abono</button>
    </form>

    <h3 class="mt-4">Historial de Abonos</h3>
    <table class="table table-striped mt-2">
        <thead>
            <tr>
                <th>Fecha</th>
                <th>Tipo</th>
                <th>Monto</th>
                <th>Descuento (%)</th>
                <th>Usuario</th>
            </tr>
        </thead>
        <tbody>
            {% for pago in pagos_realizados %}
            <tr>
                <td>{{ pago.fecha }}</td>
                <td>{{ pago.tipo }}</td>
                <td>Q{{ pago.abono }}</td>
                <td>{% if pago.descuento_porcentaje %}{{ pago.descuento_porcentaje }}%{% else %}No aplicable{% endif %}</td>
                <td>{{ pago.usuario.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Prevenir envío automatico al presionar "Enter"
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
        }
    });

    // Mostrar campo de descuento si se selecciona "TOTALIDAD"
    document.getElementById('id_tipo').addEventListener('change', function() {
        if (this.value == 'TOTALIDAD') {
            document.getElementById('descuento-container').style.display = 'block';
        } else {
            document.getElementById('descuento-container').style.display = 'none';
        }
    });

    // Calcular el nuevo total con el descuento aplicado
    function calcularDescuento() {
        var descuento = parseFloat(document.getElementById('id_descuento_porcentaje').value) || 0;
        var queda = parseFloat({{ queda }});
        var nuevo_total = queda - (queda * descuento / 100);

        // Mostrar el nuevo total y actualizar el campo de abono
        document.getElementById('nuevo_total').innerText = nuevo_total.toFixed(2);
        document.getElementById('id_abono').value = nuevo_total.toFixed(2);
    }


    
</script>






{% endblock %}
