from Producto import views,viewsprodfactura
from django.urls import path
from .viewsprodfactura import lista


urlpatterns = [
    path('',views.nuevo,name="NuevoProducto"),
    path('listadoproductos/',views.listado,name="ListaProducto"),
    path('listadoproductos2/',views.listado2,name="ListaProducto2"),
    path('eliminarproducto/<int:id>',views.eliminar,name="DeleteProducto"),
    path('ingresofactura/',viewsprodfactura.nuevo,name='IngresoFactura'),
    path('listaingresos/',viewsprodfactura.lista,name='ListaIngresoFactura'),
    path('detalleingresofactura/<int:f>',viewsprodfactura.detalle,name='DetalleIngresoFactura'),
    path('bajaingresofactura/<int:f>',viewsprodfactura.darbaja,name='BajaIngresoFactura'),
    path('modificarproducto/<int:id>',views.actualizar,name="UpdateProducto"),
    path('ventas-y-facturas/', lista, name='ventas_y_facturas'),


]
