{% extends 'BaseInicio/base.html' %}
{% block title %}Nuevo Cliente{% endblock %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}

<br></br>
    <a href="{% url 'ListaCliente' %}" class="btn btn-warning">Listado de Clientes </a>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>


        <!-- Basic Layout -->
        <div class="col-md-12">
            <div class="card md-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h5 class="mb-0">Formulario Nuevo Cliente</h5>
                    <small class="text-muted float-end">Cliente</small>
                </div>
                <div class="card-body">
                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <label>Nit</label>
                                {{form.nit}}
                            </div>
                            <div class="col-md-4">
                                <label>Nombre
                                    Cliente</label>
                                {{form.nombre}}
                            </div>
                            <div class="col-md-4">
                                <label>Telefono
                                    Cliente</label>
                                {{form.tel}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-12">
                                <label>Direccion</label>
                                {{form.direccion}}
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <label>Fecha</label>
                                <input type="text" class="form-control" readonly value="{% now 'd-m-Y' %}" />
                            </div>
                            <div class="col-md-4">
                                <label>Usuario</label>
                                <input type="text" class="form-control" readonly value="{{user.username}}" />
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Guardar</button>
                                <button type="reset" class="btn btn-danger">Cancelar</button>
                            </div>
                        </div>
                </div>
                </form>
            </div>
        </div>


    </div>

</div>



{% endblock %}