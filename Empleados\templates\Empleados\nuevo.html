{% extends 'BaseInicio/base.html' %}
{% block content %}

<br></br>
<div class="container" style="background-color: dark; margin-top: 5px; max-width: 90%;"><br>
<a href="{% url 'Listado' %}" class="btn btn-warning">Regresar</a>

    <form action="#" method="POST" enctype="">{% csrf_token %}
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-2 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-2 pb-2">
                        <h5 class="mb-0">Formulario Nuevo Trabajador</h5>
                    </div>
                </div>
           
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">DPI</label>
                            <input type="text" class="form-control" name="dpi" autofocus="dpi" required maxlength="13">
                        </div>
                       
                        <div class="col-md-3">
                            <label class="form-label">Nombres</label>
                            <input type="text" class="form-control" name="nombre" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">Apellido</label>
                            <input type="text" class="form-control" name="apellido" required>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Teléfono</label>
                            <input type="text" class="form-control" name="telefono" required>
                        </div>
                    </div>
                    <br>

                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Dirección</label>
                            <input type="text" class="form-control" name="direccion" required>
                        </div>

                        <div class="col-md-2">
                            <label>Estado Civil</label>
                            <select name="estado_civil" class="form-control" required>
                                <option value="Soltero">Soltero</option>
                                <option value="Casado">Casado</option>
                                <option value="Unido">Unido</option>
                                <option value="Divorciado">Divorciado</option>
                                <option value="Viudo">Viudo (a)</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Puesto</label>
                            <input type="text" class="form-control" name="puesto" required>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Salario Base</label>
                            <input type="text" class="form-control" name="salario_base" required>
                        </div>
                    </div>
                    <br>

                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Contacto de Emergencia</label>
                            <input type="text" class="form-control" name="contacto_emergencia" required>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Teléfono Contacto</label>
                            <input type="text" class="form-control" name="telefono_contacto" required>
                        </div>

                        <div class="col-md-3">
                            <label>Usuario</label>
                            <input type="text" class="form-control" readonly value="{{user.username}}" />
                        </div>

                        <div class="col-md-3">
                            <label>Fecha</label>
                            <input type="text" class="form-control" name="fecha" readonly value="{% now 'd-m-Y' %}" required>
                        </div>
                    </div>
                    <br>

                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">Guardar</button>
                            <a href="{% url 'Inicio' %}" class="btn btn-danger">Cancelar</a>
                        </div>
                    </div>
                    <br>
                </div>
            </div>
        </div>
    </form>

    <!-- Bloque para mostrar mensajes -->
    <div class="col-md-12">
        {% if messages %}
            <div class="alert alert-dismissible fade show" role="alert">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>

</div>

{% endblock %}
