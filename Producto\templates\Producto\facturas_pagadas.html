{% extends 'BaseInicio/base.html' %}

{% block content %}
<br></br>
<div class="container" style="max-width: 90%;">
    <a href="{% url 'ListaPagoPendienteFactura' %}" class="btn btn-warning">Regresar</a>
    <br></br>
    <form method="get" class="form-inline mb-3">
    <input type="text" name="q" class="form-control form-control-lg mr-2 w-75" placeholder="Buscar por proveedor o factura..." value="{{ request.GET.q }}">
    <button type="submit" class="btn btn-primary">Buscar</button>
    </form>

    <h2>Lista de Facturas Pagadas</h2>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Factura</th>
                <th>Serie</th>
                <th>Fecha de Factura</th>
                <th>Proveedor</th>
                <th>Monto Total</th>
                <th>Estado</th>
                <th>Detalle</th>
            </tr>
        </thead>
        <tbody>
            {% for factura in datos.facturas_pagadas %}
            <tr>
                <td>{{ factura.factura }}</td>
                <td>{{ factura.serie }}</td>
                <td>{{ factura.fecha_factura|date:"Y-m-d" }}</td>
                <td>{{ factura.id_prov.nombre }}</td>
                <td>{{ factura.total }}</td>
                <td>
                    {% if factura.estado == 1 %}
                        Pagada
                    {% elif factura.estado == 3 %}
                        A Crédito
                    {% elif factura.estado == 4 %}
                        Cancelada
                    {% endif %}
                </td>
                <td>
                                    {% if factura.detallefactura_set.exists %}
                                        <button class="btn btn-info btn-sm" data-toggle="modal" 
                                                data-target="#facturaModal" onclick="loadFacturaDetail({{ factura.pk }})">
                                            <i class="fas fa-eye"></i> Ver
                                        </button>
                                    {% else %}
                                        <button class="btn btn-danger btn-sm" onclick="confirmDelete({{ factura.pk }})">
                                            <i class="fas fa-trash"></i> 
                                        </button>
                                    {% endif %}
                                
            </tr>
            {% empty %}
            <tr>
                <td colspan="7">No hay facturas pagadas registradas.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

   
</div>

<!-- Modal para ver los detalles de la factura -->
<div class="modal fade" id="facturaModal" tabindex="-1" aria-labelledby="facturaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="facturaModalLabel">Detalle de Factura</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Aquí se cargará el contenido de la factura -->
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(facturaId) {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "No podrás revertir esta acción",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminar'
        }).then((result) => {
            if (result.isConfirmed) {
                // Obtener el csrf_token desde el cookie
                const csrftoken = getCookie('csrftoken');
    
                fetch(`/producto/factura/${facturaId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken  // Agregar el CSRF token en los headers
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.deleted) {
                        location.reload();
                    } else {
                        Swal.fire('Error', data.error, 'error');
                    }
                });
            }
        });
    }
    
    // Función para obtener el valor del csrf_token de las cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

function loadFacturaDetail(facturaId) {
    fetch(`/producto/factura/${facturaId}/`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('modal-body').innerHTML = html;
        });
}
</script>

<!-- Script para filtro de búsqueda en la tabla -->
<script type="text/javascript">
(function (document) {
    'use strict';

    var LightTableFilter = (function (Arr) {
        var _input;

        function _onInputEvent(e) {
            _input = e.target;
            var tables = document.getElementsByClassName(_input.getAttribute('data-table'));
            Arr.forEach.call(tables, function (table) {
                Arr.forEach.call(table.tBodies, function (tbody) {
                    Arr.forEach.call(tbody.rows, _filter);
                });
            });
        }

        function _filter(row) {
            var text = row.textContent.toLowerCase(), val = _input.value.toLowerCase();
            row.style.display = text.indexOf(val) === -1 ? 'none' : 'table-row';
        }

        return {
            init: function () {
                var inputs = document.getElementsByClassName('light-table-filter');
                Arr.forEach.call(inputs, function (input) {
                    input.oninput = _onInputEvent;
                });
            }
        };
    })(Array.prototype);

    document.addEventListener('readystatechange', function () {
        if (document.readyState === 'complete') {
            LightTableFilter.init();
        }
    });

})(document);
</script>
{% endblock %}
