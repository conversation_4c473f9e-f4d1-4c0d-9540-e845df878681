from django.urls import path
from Empleados import views

urlpatterns = [

    path('nuevo/',views.nuevo,name='NuevoEmpleado'),
    path('modificar/<int:id>',views.modificar,name='Modificar'),
    path('eliminar/<int:id>',views.eliminar,name='Eliminar'),
    path('listado/',views.lista,name='Listado'),
    ############# PLANILLA #############
    path('crear-planilla/',views.crear_planilla_y_agregar_trabajadores, name='crear_planilla'),
    path('agregar-trabajador/<int:planilla_id>/', views.agregar_trabajador, name='agregar_trabajador'),

    path('empleado/buscar/', views.buscar_empleado, name='buscar_empleado'),
    path('eliminarplanilla/<int:id>',views.eliminarplanilla,name='EliminarPlanilla'),
    path('listadoplanilla/',views.listaplanilla,name='ListadoPlanilla'),
   # path('terminarplanilla/', views.TerminarPlanilla, name='terminar_planilla'),  # Asegúrate de que esta línea esté presente
]
