from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from datetime import datetime
from django.contrib import messages
from django.urls import reverse
from Producto.models import Producto
from Venta.models import Venta,Detalle,Gasto,PagoCreditoFel
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum
from django.http import HttpResponse, JsonResponse
from .reportes import Comprobante,Cotizacion
from .forms import GastoForm
from decimal import Decimal, InvalidOperation
from Cliente.models import Cliente  # Importa el modelo Cliente desde la aplicación de clientes
from .models import Venta
from django.utils.timezone import localdate
from datetime import date

from datetime import datetime, timedelta

import emisor
import receptor
import InfileFel
from django.http import HttpResponse


@login_required
def eliminar_venta(request, venta_id):
    if request.method == "POST":
        venta = get_object_or_404(Venta, pk=venta_id)
        venta.delete()
        return JsonResponse({'deleted': True})
    return JsonResponse({'deleted': False}, status=400)



@login_required
def detalle_venta_cliente(request, venta_id):
    venta = get_object_or_404(Venta, pk=venta_id)
    
    return render(request, 'Venta/detalle_venta_cliente.html', {'venta': venta})


from datetime import datetime, date, timedelta

def ventashoy(request):
    hoy_inicio = datetime.combine(date.today(), datetime.min.time())
    hoy_fin = hoy_inicio + timedelta(days=1)

    ventas_hoy = Venta.objects.filter(
        fecha__gte=hoy_inicio,
        fecha__lt=hoy_fin,
        estado__in=[1, 2],  # estados que quieres incluir
    ).exclude(
        tipo__iexact='cotizacion'  # excluir cotizaciones, case insensitive
    ).order_by('-factura', '-fecha')

    return render(request, 'Venta/ventashoy.html', {'hoy': ventas_hoy})



def ventashoy2(request):
    hoy_inicio = datetime.combine(date.today(), datetime.min.time())
    hoy_fin = hoy_inicio + timedelta(days=1)

    ventas_hoy = Venta.objects.filter(
        fecha__gte=hoy_inicio,
        fecha__lt=hoy_fin,
        estado__in=[1, 2],  # Mostrar ventas con estado Pagada (1) y Crédito (3)
    ).exclude(
        tipo__iexact='cotizacion'  # Excluir cotizaciones
    ).order_by('-factura', '-fecha')

    return render(request, 'Venta/ventashoy2.html', {'hoy': ventas_hoy})

    

from django.utils.dateparse import parse_date

@login_required
def todasventas(request):
    search = request.GET.get('search', '').strip()
    fecha_param = request.GET.get('fecha', '').strip()

    # Optimización 1: select_related para evitar N+1 queries
    ventas = Venta.objects.select_related('usuario').filter(estado__lte=3)

    # Optimización 2: Aplicar filtros de manera eficiente
    if fecha_param:
        fecha_parseada = parse_date(fecha_param)
        if fecha_parseada:
            ventas = ventas.filter(fecha=fecha_parseada)

    if search:
        # Optimización 3: Usar Q objects de manera más eficiente
        ventas = ventas.filter(
            Q(factura__icontains=search) |
            Q(nombre__icontains=search) |
            Q(fecha__icontains=search) |
            Q(usuario__username__icontains=search)
        )

    # Optimización 4: Ordenar solo por un campo para mejor performance
    ventas = ventas.order_by('-factura')

    # Optimización 5: Limitar resultados si no hay búsqueda específica
    if not search and not fecha_param:
        # Si no hay filtros, mostrar solo las últimas 500 ventas para mejor rendimiento
        ventas = ventas[:500]

    # Optimización 6: Usar only() para cargar solo los campos necesarios
    ventas = ventas.only(
        'factura', 'nombre', 'total', 'estado', 'fecha', 'tipo', 'token', 'link',
        'usuario__username'
    )

    context = {
        'todas': ventas,
        'search': search,
        'fecha': fecha_param,
    }

    return render(request, 'Venta/todasventas.html', context)


from django.core.paginator import Paginator

@login_required
def todasventascredito(request):
    # Excluir CreditoFel de las ventas a crédito normales
    todas = Venta.objects.filter(estado=3).exclude(tipo='CreditoFel').order_by('-fecha')
    return render(request, 'Venta/todasventascredito.html', {'todas': todas})



@login_required
def todasventas2(request):
    todas = Venta.objects.filter(estado__lte=2).order_by('-factura','-fecha')  # Ordenar por fecha descendente
    return render(request, 'Venta/todasventas2.html', {'todas': todas})

@login_required
def lista_ventas_pagadas(request):
    # Filtrar las ventas que están pagadas, a crédito o canceladas
    ventas_pagadas = Venta.objects.filter(
        estado__in=[Venta.ESTADO_PAGADA, Venta.ESTADO_CANCELADA]
    ).order_by('-factura','-fecha')
    return render(request, 'Venta/ventas_pagadas.html', {'ventas_pagadas': ventas_pagadas})



cont = 0
@login_required
def venta(request):
    ultima = Venta.objects.order_by('factura').last()
    nueva = ultima.factura + 1 if ultima else 1  # Asigna un valor inicial si no hay ventas previas

    if request.method == "POST":

        if request.POST['tipo_venta'] == "Cotizacion":

            nit = request.POST.get("nit", "").strip()
            nombre = request.POST.get("nombre", "").strip()
            direccion = request.POST.get("direccion", "").strip()

            if not nit:  # Si no se proporciona un NIT, asigna valores predeterminados
                nit = 'CF'
                nombre = 'Consumidor Final'
                direccion = 'Ciudad'

            cliente = Cliente.objects.filter(nit=nit).first()

            if not cliente:  # Si el cliente no existe, se crea uno nuevo
                cliente = Cliente(nit=nit, nombre=nombre, direccion=direccion)
                cliente.usuario = request.user  # Asigna el objeto User completo
                cliente.save()

            v = Venta()
            v.tipo = request.POST['tipo_venta']
            v.nit = cliente.nit
            v.nombre = cliente.nombre
            v.direccion = cliente.direccion
            v.estado = 0
            v.usuario = request.user  # Asigna el objeto User completo
            v.fecha = datetime.today()
            v.save()
            messages.success(request, 'Cotizacion Iniciada')
            return redirect("DetalleCotizacion", v.token)
        else:
            nit = request.POST.get("nit", "").strip()
            nombre = request.POST.get("nombre", "").strip()
            direccion = request.POST.get("direccion", "").strip()

            if not nit:  # Si no se proporciona un NIT, asigna valores predeterminados
                nit = 'CF'
                nombre = 'Consumidor Final'
                direccion = 'Ciudad'

            cliente = Cliente.objects.filter(nit=nit).first()

            if not cliente:  # Si el cliente no existe, se crea uno nuevo
                cliente = Cliente(nit=nit, nombre=nombre, direccion=direccion)
                cliente.usuario = request.user  # Asigna el objeto User completo
                cliente.save()

            v = Venta()
            v.nit = cliente.nit
            v.nombre = cliente.nombre
            v.tipo = request.POST['tipo_venta']
            v.direccion = cliente.direccion
            v.estado = 0
            v.usuario = request.user  # Asigna el objeto User completo
            v.fecha = datetime.today()
            v.save()
            messages.success(request, 'Venta Iniciada')
            return redirect("Detalle", v.token)

    return render(request, "Venta/venta.html", {'v': nueva})
        
        


@login_required
def detalle(request, f):
    venta = Venta.objects.get(token=f)
    det = Detalle.objects.filter(token=f)
    b = False
    tipo_venta = venta.tipo  # El campo que contiene el tipo de venta
    if venta:
        if request.method == "POST":
            # Buscar productos
            if 'buscar' in request.POST:
                if request.POST['buscar'] == "":
                    messages.error(request, 'Campo No Puede Estar Vacio')
                    return redirect('Detalle', f)
                else:
                    # Crear el filtro para buscar por codigo o nombre
                    querys = (Q(codigo__icontains=request.POST['buscar']) | Q(nombre__icontains=request.POST['buscar']))
                    # Filtrar y ordenar los productos que coinciden con la búsqueda alfabéticamente por nombre
                    busqueda = Producto.objects.filter(querys).order_by('nombre')  # Ordenar por 'nombre'
                    b = True
                    # Renderizar la plantilla con los productos encontrados
                    return render(request, 'Venta/detalle.html', {
                        'venta': venta,
                        'detalle': det,
                        'busq': busqueda,
                        'b': b
                    })

            # Agregar productos a la venta
            elif 'agregar' in request.POST:
                # Asegurarte de que 'existencia' esté definida antes de usarla
                if Producto.objects.filter(id=request.POST['id']).exists():
                    existencia = Producto.objects.get(id=request.POST['id'])

                    # Verificar existencia en stock
                    if existencia.stock < int(request.POST['cantidad']):
                        messages.error(request, f'{existencia.codigo} No Tiene Existencia!')
                        return redirect('Detalle', f)
                    else:
                        # Crear una nueva fila en Detalle para cada instancia del producto
                        d = Detalle()
                        d.factura = Venta.objects.get(factura=venta.factura)
                        d.id_inventario = Producto.objects.get(id=existencia.id)
                        d.cantidad = int(request.POST['cantidad'])
                        d.precio_uni = existencia.precio_venta

                        # Verificar si 'descuento' es un valor válido antes de convertirlo a Decimal
                        descuento_str = request.POST.get('descuento', '0').strip()  # Si está vacío, será '0'
                        try:
                            descuento = Decimal(descuento_str)  # Intentar convertir a Decimal
                        except (InvalidOperation, ValueError):
                            descuento = Decimal(0)  # Si no es válido, asignar 0
            
                        
                        d.descuento = d.cantidad * descuento
                        d.subtotal = d.cantidad * d.precio_uni
                        d.total = d.subtotal - d.descuento
                        d.estado = 1
                        d.fecha = datetime.today()
                        d.usuario = User.objects.get(id=request.user.id)
                        d.token = f
                        d.save()
                        
                        # Actualizar el stock del producto
                        existencia.stock -= d.cantidad
                        existencia.save()
                        
                        # Actualizar total de la venta
                        venta.total += (d.subtotal - d.descuento)
                        venta.save()
                        
                        messages.success(request, f'Agregando {request.POST["cantidad"]} Unidad de {existencia.nombre}')
                        return redirect('Detalle', f)

            # Quitar productos de la venta
            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(id=request.POST['corr'], token=f)
                elprod = Producto.objects.get(id=idetalle.id_inventario.id)
                
                # Actualizar total de la venta
                venta.total -= idetalle.total
                venta.save()
                
                Producto.objects.filter(id=idetalle.id_inventario.id).update(
                    stock=elprod.stock + idetalle.cantidad, salio=elprod.salio - idetalle.cantidad
                )
                idetalle.delete()
                
                messages.success(request, 'Producto quitado')
                return redirect('Detalle', f)

            # Aplicar descuento total
            elif 'descuento_total' in request.POST:
                descuento = Decimal(request.POST.get('descuento_total', 0))
                venta.descuento_total += descuento
                venta.total -= descuento
                venta.save()
                
                messages.success(request, "Descuento aplicado correctamente.")
                return redirect('Detalle', f)

            # Editar producto en la venta
            elif 'nombre_producto' in request.POST:
                corr = request.POST.get('corr')
                detalle = Detalle.objects.get(id=corr)
                
                nuevo_nombre = request.POST.get('nombre_producto')
                if nuevo_nombre != detalle.id_inventario.nombre:
                    detalle.nombre_custom = nuevo_nombre

                nuevo_precio_str = request.POST.get('precio_producto', '0')
                nuevo_precio = Decimal(nuevo_precio_str.replace(',', '.'))
                if nuevo_precio != detalle.precio_uni:
                    detalle.precio_custom = nuevo_precio
                    detalle.precio_uni = nuevo_precio

                # Recalcular el subtotal y el total del detalle
                detalle.subtotal = detalle.cantidad * detalle.precio_uni
                detalle.total = detalle.subtotal - detalle.descuento
                detalle.save()

                # Recalcular el total de la venta
                venta.total = Detalle.objects.filter(factura=venta.factura, token=f).aggregate(total=Sum('total'))['total'] or 0
                venta.save()

                messages.success(request, "Producto editado correctamente.")
                return redirect('Detalle', f)

            # Finalizar venta
            elif 'terminar' in request.POST:
                if 'credito' in request.POST:
                    # Verificar si es una venta FEL (tipo 'Contado')
                    if venta.tipo == 'Contado':
                        # Para ventas FEL a crédito: procesar FEL pero mantener estado=3
                        venta.tipo = 'CreditoFel'
                        venta.estado = 3
                        venta.nit = request.POST['nit']
                        venta.nombre = request.POST['nombre']
                        venta.direccion = request.POST['direccion']
                        venta.save()

                        # Procesar FEL automáticamente
                        try:
                            fel2(request, f)
                            # Después del FEL, asegurar que el estado siga siendo 3 (crédito)
                            venta.refresh_from_db()
                            venta.estado = 3
                            venta.tipo = 'CreditoFel'
                            venta.save()
                            messages.success(request, 'Venta FEL a crédito procesada exitosamente')
                        except Exception as e:
                            messages.error(request, f'Error al procesar FEL: {str(e)}')

                        return redirect('Venta')
                    else:
                        # Para otros tipos de venta a crédito (mantener lógica original)
                        venta.estado = 3
                        venta.tipo = 'Credito'
                        venta.nit = request.POST['nit']
                        venta.nombre = request.POST['nombre']
                        venta.direccion = request.POST['direccion']
                        venta.save()
                        messages.success(request, f)
                        return redirect('Venta')
                else:
                    venta.estado = 1
                    venta.nit = request.POST['nit']
                    venta.nombre = request.POST['nombre']
                    venta.direccion = request.POST['direccion']
                    venta.save()
                    return redirect('CobroVenta',f)

    else:
        messages.error(request, 'Venta Terminada Token Corrupto')
        return redirect('Venta')

    subtalVenta = venta.descuento_total + venta.total
    return render(request, 'Venta/detalle.html', {'venta': venta, 'detalle': det, 'b': b, 'subtalVenta': subtalVenta, 'tipo_venta': tipo_venta})



def ver_detalles_venta(request, venta_id):
    try:
        venta = Venta.objects.get(id=venta_id)
    except Venta.DoesNotExist:
        # Si no se encuentra la venta, puedes redirigir o mostrar un mensaje de error
        return render(request, 'venta/error.html', {'error_message': 'Venta no encontrada'})

    detalles = Detalle.objects.filter(factura=venta)
    return render(request, 'venta/ver_detalle.html', {'venta': venta, 'detalles': detalles})



@login_required
def listacotizacion(request):
    fecha_inicio = request.GET.get('fecha_inicio')
    fecha_fin = request.GET.get('fecha_fin')

    coti = Venta.objects.filter(
        estado=1,
        tipo__iexact='cotizacion'
    )

    if fecha_inicio:
        try:
            fecha_inicio_obj = datetime.strptime(fecha_inicio, '%Y-%m-%d')
            coti = coti.filter(fecha__gte=fecha_inicio_obj)
        except ValueError:
            pass

    if fecha_fin:
        try:
            fecha_fin_obj = datetime.strptime(fecha_fin, '%Y-%m-%d')
            coti = coti.filter(fecha__lte=fecha_fin_obj)
        except ValueError:
            pass

    coti = coti.prefetch_related('detalle_set').order_by('-fecha')

    return render(request, 'Venta/cotizacion.html', {
        'coti': coti,
        'fecha_inicio': fecha_inicio,
        'fecha_fin': fecha_fin,
    })




@login_required
def anular_cotizacion(request, token):
    venta = get_object_or_404(Venta, token=token)
    venta.estado = 2  # Asumimos que 2 es "anulada"
    venta.save()
    return redirect('lista_cotizacion')  # redirige de nuevo a la lista


@login_required
def cotizacion(request):
        print('Cotizacion')
        v = Venta()
        v.nit = 'CF'
        v.nombre = "Consumidor Final"
        v.direccion = "Ciudad"   
        v.total = 0.00
        v.estado = 0
        v.fecha = datetime.today().strftime('%Y-%m-%d')
        v.usuario = User.objects.get(id=request.user.id)
        # El token se genera automáticamente por el modelo
        v.save()
        messages.success(request,'Cotizacion Iniciada')
        return redirect('DetalleCotizacion',v.token)


@login_required
def detallecotizacion(request,f):
    venta = Venta.objects.get(token=f)
    det = Detalle.objects.filter(token=f)
    b = False

    if venta:
        if request.method == "POST":
            # Buscar productos
            if 'buscar' in request.POST:
                if request.POST['buscar'] == "":
                    messages.error(request, 'Campo No Puede Estar Vacio')
                    return redirect('Detalle', f)
                else:
                    # Crear el filtro para buscar por codigo o nombre
                    querys = (Q(codigo__icontains=request.POST['buscar']) | Q(nombre__icontains=request.POST['buscar']))
                    # Filtrar y ordenar los productos que coinciden con la búsqueda alfabéticamente por nombre
                    busqueda = Producto.objects.filter(querys).order_by('nombre')  # Ordenar por 'nombre'
                    b = True
                    # Renderizar la plantilla con los productos encontrados
                    return render(request, 'Venta/detallecotizacion.html', {
                        'venta': venta,
                        'detalle': det,
                        'busq': busqueda,
                        'b': b
                    })

            # Agregar productos a la venta
            elif 'agregar' in request.POST:
                # Asegurarte de que 'existencia' esté definida antes de usarla
                if Producto.objects.filter(id=request.POST['id']).exists():
                    existencia = Producto.objects.get(id=request.POST['id'])

                   
                    # Crear una nueva fila en Detalle para cada instancia del producto
                    d = Detalle()
                    d.factura = Venta.objects.get(factura=venta.factura)
                    d.id_inventario = Producto.objects.get(id=existencia.id)
                    d.cantidad = int(request.POST['cantidad'])
                    d.precio_uni = existencia.precio_venta

                    # Verificar si 'descuento' es un valor válido antes de convertirlo a Decimal
                    descuento_str = request.POST.get('descuento', '0').strip()  # Si está vacío, será '0'
                    try:
                        descuento = Decimal(descuento_str)  # Intentar convertir a Decimal
                    except (InvalidOperation, ValueError):
                        descuento = Decimal(0)  # Si no es válido, asignar 0
            
                    d.descuento = d.cantidad * descuento
                    d.subtotal = d.cantidad * d.precio_uni
                    d.total = d.subtotal - d.descuento
                    d.estado = 1
                    d.fecha = datetime.today()
                    d.usuario = User.objects.get(id=request.user.id)
                    d.token = f
                    d.save()
                        
                    # Actualizar total de la venta
                    venta.total += (d.subtotal - d.descuento)
                    venta.save()
                        
                    messages.success(request, f'Agregando {request.POST["cantidad"]} Unidad de {existencia.nombre}')
                    return redirect('DetalleCotizacion', f)

            # Quitar productos de la venta
            elif 'quitar' in request.POST:
                idetalle = Detalle.objects.get(id=request.POST['corr'], token=f)
                elprod = Producto.objects.get(id=idetalle.id_inventario.id)
                
                # Actualizar total de la venta
                venta.total -= idetalle.total
                venta.save()
                
                idetalle.delete()
                
                messages.success(request, 'Producto quitado')
                return redirect('DetalleCotizacion', f)

            # Aplicar descuento total
            elif 'descuento_total' in request.POST:
                descuento = Decimal(request.POST.get('descuento_total', 0))
                venta.descuento_total += descuento
                venta.total -= descuento
                venta.save()
                
                messages.success(request, "Descuento aplicado correctamente.")
                return redirect('DetalleCotizacion', f)

            # Editar producto en la venta
            elif 'nombre_producto' in request.POST:
                corr = request.POST.get('corr')
                detalle = Detalle.objects.get(id=corr)
                
                nuevo_nombre = request.POST.get('nombre_producto')
                if nuevo_nombre != detalle.id_inventario.nombre:
                    detalle.nombre_custom = nuevo_nombre

                nuevo_precio_str = request.POST.get('precio_producto', '0')
                nuevo_precio = Decimal(nuevo_precio_str.replace(',', '.'))
                if nuevo_precio != detalle.precio_uni:
                    detalle.precio_custom = nuevo_precio
                    detalle.precio_uni = nuevo_precio

                # Recalcular el subtotal y el total del detalle
                detalle.subtotal = detalle.cantidad * detalle.precio_uni
                detalle.total = detalle.subtotal - detalle.descuento
                detalle.save()

                # Recalcular el total de la venta
                venta.total = Detalle.objects.filter(factura=venta.factura, token=f).aggregate(total=Sum('total'))['total'] or 0
                venta.save()

                messages.success(request, "Producto editado correctamente.")
                return redirect('DetalleCotizacion', f)

            # Finalizar venta
            elif 'terminar' in request.POST:
                    venta.estado = 1
                    venta.tipo = 'Cotizacion'
                    venta.nit = request.POST['nit']
                    venta.nombre = request.POST['nombre']
                    venta.direccion = request.POST['direccion']
                    venta.save()
                    messages.success(request, f)
                    return redirect('Venta')

                

    else:
        messages.error(request, 'Venta Terminada Token Corrupto')
        return redirect('Venta')

    subtalVenta = venta.descuento_total + venta.total
    return render(request, 'Venta/detallecotizacion.html', {'venta': venta, 'detalle': det, 'b': b, 'subtalVenta': subtalVenta})



@login_required
def cobro(request,f):

    v = Venta.objects.get(token=f)

    if request.method == "POST":

        if v.tipo == 'Contado':
            fel(request,f)
            messages.info(request, v.link)
            return redirect('Venta')

        else:
            messages.success(request, f)
            return redirect('Venta')
    
        


    return render(request,'Venta/pago.html',{'v':v})


@login_required
def gasto(request):
    form = GastoForm()
    if request.method == "POST":
        form = GastoForm(request.POST)
        if form.is_valid():
            try:
                g = Gasto()
                g.nombre = form.cleaned_data['nombre']
                g.cantidad = form.cleaned_data['cantidad']
                g.precio_uni = form.cleaned_data['precio_uni']
                g.total = g.cantidad*g.precio_uni
                g.estado = 1
                g.fecha = datetime.today().strftime('%Y-%m-%d')
                g.usuario = request.user.username
                g.save()
                messages.success(request,f'Gasto Ingresado Correctamente!')
                return redirect('Gasto')
            except:
                messages.error(request,f'Error al Ingresar Gasto!')
                return redirect('Gasto')


    return render(request,'Venta/gastos.html',{'form':form})



@login_required
def listagasto(request):
    gastos = Gasto.objects.all()
    return render(request,'Venta/todogasto.html',{'gastos':gastos})



@login_required
def listagastohoy(request):
    print(datetime.today().strftime('%Y-%m-%d'))
    gastos = Gasto.objects.filter(fecha=datetime.today().strftime('%Y-%m-%d'))
    return render(request,'Venta/todogastohoy.html',{'gastos':gastos})
    
    


@login_required
def estadisticas(request):

    # Excluir CreditoFel de las estadísticas generales (solo contar cuando están pagados)
    todoventa = Venta.objects.filter(estado=1).exclude(tipo='CreditoFel').aggregate(tv=Sum('total'))
    totalhechas = Venta.objects.filter(estado=1).exclude(tipo='CreditoFel').count()

    todoventahoy = Venta.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).exclude(tipo='CreditoFel').aggregate(tvh=Sum('total'))
    totalhechashoy = Venta.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).exclude(tipo='CreditoFel').count()

    # Agregar estadísticas de CreditoFel pagados
    creditos_fel_pagados = Venta.objects.filter(estado=1, tipo='CreditoFel').aggregate(cfp=Sum('total'))
    creditos_fel_pagados_count = Venta.objects.filter(estado=1, tipo='CreditoFel').count()

    todogasto = Gasto.objects.aggregate(g=Sum('total'))
    todogastohoy = Gasto.objects.filter(estado=1,fecha=datetime.today().strftime('%Y-%m-%d')).aggregate(gh=Sum('total'))

    # Ingresos totales incluyendo CreditoFel pagados
    ingresos_normales = Venta.objects.filter(estado=1).exclude(tipo='CreditoFel').aggregate(i=Sum('total'))
    ingresos_creditos_fel = Venta.objects.filter(estado=1, tipo='CreditoFel').aggregate(icf=Sum('total'))

    ingresos_total = (ingresos_normales['i'] or 0) + (ingresos_creditos_fel['icf'] or 0)
    egresos = Gasto.objects.filter(estado=1).aggregate(e=Sum('total'))
    
    if todogastohoy['gh'] == None:
        todogastohoy['gh'] = Decimal(0.00)
    else:
        totalhechashoy['gh']

    if todoventahoy['tvh'] == None:
        todoventahoy['tvh'] = Decimal(0.00)
    else:
        todoventahoy['tvh']
    
    if ingresos_total == None:
        ingresos_total = Decimal(0.00)

    if egresos['e'] == None:
        egresos['e'] = Decimal(0.00)
    else:
        egresos['e']


    li = ingresos_total - egresos['e']
    li_h = todoventahoy['tvh']-todogastohoy['gh']



    return render(request,'Venta/estadistica.html',{
        'tdv':todoventa['tv'],
        'th':totalhechas,
        'tvh':totalhechashoy,
        'thh':todoventahoy['tvh'],
        'tdg':todogasto['g'],
        'tdgh':todogastohoy['gh'],
        'i':ingresos_total,
        'e':egresos['e'],
        'l1':li,
        'l2':li_h,
        'creditos_fel_pagados': creditos_fel_pagados['cfp'] or 0,
        'creditos_fel_pagados_count': creditos_fel_pagados_count
    })
    
    

@login_required
def descartar(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salio=p.salio-d.cantidad)
            else:
                pass   

    dsdetalle.delete()
    dsventa.delete()
    messages.success(request,'Venta Descartada')
    return redirect('Venta') 


@login_required
def anularventa(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salio=p.salio-d.cantidad)
            else:
                pass   

    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Venta Anulada Productos Vuelven a Stock')
    return redirect('ListaVentaHoy') 
    

@login_required
def anularventa2(request,f):
    dsdetalle = Detalle.objects.filter(token=f)
    dsventa = Venta.objects.get(token=f)
    producs = Producto.objects.all()
    

    for d in dsdetalle:
        for p in producs:
            if p.id == d.id_inventario.id:
                Producto.objects.filter(id=d.id_inventario.id).update(stock=p.stock+d.cantidad,salio=p.salio-d.cantidad)
            else:
                pass   

    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Venta Anulada Productos Vuelven a Stock')
    return redirect('ListaVentaTodas') 
    


@login_required
def anularcotizacion(request,f):
    Detalle.objects.filter(token=f).update(estado=2)
    Venta.objects.filter(token=f).update(estado=2)
    messages.success(request,'Cotizacion Anulada')
    return redirect('Cotizacion')



################  editar cotizacion  ####################

def pdf(request,f):
   if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
   else: 
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="comprobante-venta-#-{f}.pdf"'
    r = Comprobante(f)
    response.write(r.run())
    return response  


################## PDF COTIZACION  #############################

def pdfcotizacion(request, f):
    if not request.user.is_authenticated or not request.user.is_active:
        return redirect('/')
    else: 
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="cotizacion-{f}.pdf"'
        r = Cotizacion(f)  # Usamos una clase nueva Cotizacion, similar a Comprobante
        response.write(r.run())
        return response


###########################   PDF CREDITO  #####################################

from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from .models import Venta, Abono  # Asegúrate de tener el modelo Abono
from django.template.loader import render_to_string


@login_required
def pdf_credito(request, f):
    venta = get_object_or_404(Venta, factura=f)
    abonos = Abono.objects.filter(venta=venta)  # Asumiendo que tienes un modelo Abono relacionado
    saldo = venta.total - abonos.aggregate(Sum('monto'))['monto__sum'] if abonos.exists() else venta.total

    # Determinar el estado de la venta
    if venta.estado == Venta.ESTADO_CREDITO and saldo <= 0:
        estado = 'Cancelada'
    else:
        estado = 'Activa'  # o cualquier otro estado que desees mostrar

    # Renderizar la plantilla para el PDF
    html_string = render_to_string('Venta/pdf_credito_venta.html', {
        'venta': venta,
        'abonos': abonos,
        'saldo': saldo,
        'estado': estado,
    })

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="venta_{venta.factura}.pdf"'


    return response


from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from .models import Venta, Detalle
import InfileFel
import emisor
import receptor

cont = 0

@login_required
def pasa_a_fel(request, t):
    try:
        # Descontar del inventario antes de pasar a FEL
        detalles = Detalle.objects.filter(token=t)
        
        for detalle in detalles:
            producto = detalle.id_inventario
            # Verificar que hay suficiente stock
            if producto.stock >= detalle.cantidad:  # Cambiado a 'stock'
                producto.stock -= detalle.cantidad  # Cambiado a 'stock'
                producto.save()
            else:
                messages.error(request, f'Stock insuficiente para {producto.nombre}')
                return redirect('ListaVentaTodas')
        
        # Cambiar tipo de venta y procesar FEL
        Venta.objects.filter(token=t).update(tipo='Contado')
        fel2(request, t)
        messages.success(request, 'Factura Se Facturo en FEL')
        return redirect('ListaVentaTodas') 
    except Exception as e:
        messages.error(request, f'Error al Pasar a FEL: {str(e)}')
        return redirect('ListaVentaTodas')

def fel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        # Obtener las ventas y detalles
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)

        # Calcular totales
        total = Detalle.objects.filter(token=t).aggregate(tot=Sum('total'))  # Total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(t=Sum('total'))  # Total de la venta

        # Aquí manejamos las cadenas de texto correctamente con UTF-8
        nit = str(request.POST["nit"])  # Aseguramos que sea un string Unicode
        nombre = str(request.POST["nombre"])  # Aseguramos que sea un string Unicode
        direccion = str(request.POST["direccion"])  # Aseguramos que sea un string Unicode

        # Actualizamos la venta
        Venta.objects.filter(token=t).update(
            nit=nit, nombre=nombre, direccion=direccion, estado=1
        )

        # Crear el DTE a Certificar
        dte_fel_a_certificar = InfileFel.fel_dte()

        # Crear el emisor
        emisor_fel = emisor.emisor()

        # Crear el receptor
        receptor_fel = receptor.receptor()

        # Crear los totales
        total_fel = InfileFel.totales()

        # Totales impuestos
        totales_impuestos = InfileFel.total_impuesto()

        # Obtener los datos del cliente para configurar los parámetros
        for datoscliente in Venta.objects.filter(token=t):
            print(datoscliente.nit, datoscliente.nombre, datoscliente.direccion, t)

            # Setear dirección del emisor
            emisor_fel.set_direccion(
                'AVENIDA SELMA ALABAMA 05-50 ZONA 1 ZACAPA,ZACAPA \n',
                '19001', 'TEL.', '7941-4049', 'GT'
            )

            # Setear datos del emisor
            emisor_fel.set_datos_emisor(
                'GEN', '2', '<EMAIL>', '30871360',
                'ELECTROCASA', 'SILVIA KARINA, OLIVA VARGAS DE ORELLANA'
            )

            # Setear dirección del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT'
            )

            # Setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre
            )

            # Identificador único del DTE
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # Ajustar el ID de manera adecuada

            # Setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT'
            )

            # Agregar los datos del emisor y receptor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # Agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')

            # Inicializar variables para calcular totales
            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0

            # Iterar sobre los detalles de la venta
            for item in Detalle.objects.filter(token=t):
                num = num + 1
                item_1 = InfileFel.item()
                item_1_impuesto = InfileFel.impuesto()

                # Usar nombre_custom si existe, sino usar el nombre regular
                nombre_producto = item.nombre_custom if item.nombre_custom else item.id_inventario.nombre
                
                # Usar precio_custom si existe, sino usar el precio regular
                precio_unitario = item.precio_custom if item.precio_custom else item.precio_uni

                # Llenar el item con los datos necesarios
                item_1.set_numero_linea(num)
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')
                item_1.set_descripcion(nombre_producto)  # Usar nombre custom
                item_1.set_precio_unitario(precio_unitario)  # Usar precio custom
                item_1.set_precio(item.cantidad * precio_unitario)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                # Calcular el IVA y el monto gravable
                grav = round((item.total / 112) * 100, 2)
                iva = round((grav * 12) / 100, 2)

                # Llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item al DTE
                dte_fel_a_certificar.agregar_item(item_1)

            # Establecer el gran total
            total_fel.set_gran_total(miventa)

            # Establecer el total de impuestos
            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)
            total_fel.set_total_impuestos(totales_impuestos)

            # Agregar los totales al DTE
            dte_fel_a_certificar.agregar_totales(total_fel)
            
            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "TELEFONO"
            fel_adenda.valor = "7941-4049"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # Realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if certificacion_fel["resultado"]:
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("NUMERO:" + str(certificacion_fel["numero"]))

                # Actualizar la venta con la información del DTE
                total = Detalle.objects.filter(token=t).aggregate(tot=Sum('total'))  # Total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}"
                )

                Venta.objects.filter(token=t).update(
                    nit=nit, nombre=nombre, direccion=direccion, fecha_fel=str(certificacion_fel['fecha']),
                    estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}",
                    anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'],
                    total=total['tot']
                )
            else:
                print("No pudo ser certificada")
                print("Descripción: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("Fuente: " + error_fel["mensaje_error"])
                    print("Categoría: " + error_fel["categoria"])
                    print("Numeral: " + error_fel["numeral"])
                    print("Validación: " + error_fel["validacion"])

        else:
            pass

def fel2(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        # Obtener las ventas y detalles
        factura = Venta.objects.filter(token=t)
        factura_ver = Venta.objects.get(token=t)
        detalle = Detalle.objects.filter(token=t)

        # Calcular totales
        total = Detalle.objects.filter(token=t).aggregate(tot=Sum('total'))  # Total de la venta
        miventa = Detalle.objects.filter(token=t).aggregate(t=Sum('total'))  # Total de la venta

        # Aquí manejamos las cadenas de texto correctamente con UTF-8
        nit = factura_ver.nit  # Aseguramos que sea un string Unicode
        nombre = factura_ver.nombre  # Aseguramos que sea un string Unicode
        direccion = factura_ver.direccion  # Aseguramos que sea un string Unicode

        # Actualizamos la venta
        Venta.objects.filter(token=t).update(
            nit=nit, nombre=nombre, direccion=direccion, estado=1
        )

        # Crear el DTE a Certificar
        dte_fel_a_certificar = InfileFel.fel_dte()

        # Crear el emisor
        emisor_fel = emisor.emisor()

        # Crear el receptor
        receptor_fel = receptor.receptor()

        # Crear los totales
        total_fel = InfileFel.totales()

        # Totales impuestos
        totales_impuestos = InfileFel.total_impuesto()

        # Obtener los datos del cliente para configurar los parámetros
        for datoscliente in Venta.objects.filter(token=t):
            print(datoscliente.nit, datoscliente.nombre, datoscliente.direccion, t)

            # Setear dirección del emisor
            emisor_fel.set_direccion(
                'AVENIDA SELMA ALABAMA 05-50 ZONA 1 ZACAPA,ZACAPA \n',
                '19001', 'TEL.', '7941-4049', 'GT'
            )

            # Setear datos del emisor
            emisor_fel.set_datos_emisor(
                'GEN', '2', '<EMAIL>', '30871360',
                'ELECTROCASA', 'SILVIA KARINA, OLIVA VARGAS DE ORELLANA'
            )

            # Setear dirección del receptor
            receptor_fel.set_direccion(
                datoscliente.direccion, '19001', 'Zacapa', 'guatemala', 'GT'
            )

            # Setear datos del receptor
            receptor_fel.set_datos_receptor(
                '<EMAIL>', datoscliente.nit, datoscliente.nombre
            )

            # Identificador único del DTE
            dte_fel_a_certificar.set_clave_unica(f'202132{id}')  # Ajustar el ID de manera adecuada

            # Setear datos generales
            dte_fel_a_certificar.set_datos_generales(
                'GTQ', f'{datoscliente.fecha}T00:00:00-06:00', 'FACT'
            )

            # Agregar los datos del emisor y receptor
            dte_fel_a_certificar.set_datos_emisor(emisor_fel)
            dte_fel_a_certificar.set_datos_receptor(receptor_fel)

            # Agregar las frases
            dte_fel_a_certificar.frase_fel.set_frase('1', '1')

            # Inicializar variables para calcular totales
            num = 1
            acutotal = 0
            acuiva = 0
            miventa = 0

            # Iterar sobre los detalles de la venta
            for item in Detalle.objects.filter(token=t):
                num = num + 1
                item_1 = InfileFel.item()
                item_1_impuesto = InfileFel.impuesto()

                # Usar nombre_custom si existe, sino usar el nombre regular
                nombre_producto = item.nombre_custom if item.nombre_custom else item.id_inventario.nombre
                
                # Usar precio_custom si existe, sino usar el precio regular
                precio_unitario = item.precio_custom if item.precio_custom else item.precio_uni

                # Llenar el item con los datos necesarios
                item_1.set_numero_linea(num)
                item_1.set_bien_o_servicio('B')
                item_1.set_cantidad(item.cantidad)
                item_1.set_unidad_medida('UND')
                item_1.set_descripcion(nombre_producto)  # Usar nombre custom
                item_1.set_precio_unitario(precio_unitario)  # Usar precio custom
                item_1.set_precio(item.cantidad * precio_unitario)
                item_1.set_descuento(0)
                item_1.set_total(item.total)

                # Calcular el IVA y el monto gravable
                grav = round((item.total / 112) * 100, 2)
                iva = round((grav * 12) / 100, 2)

                # Llenar los impuestos del item
                item_1_impuesto.set_monto_impuesto(iva)
                item_1_impuesto.set_monto_gravable(grav)
                item_1_impuesto.set_codigo_unidad_gravable(1)
                item_1_impuesto.set_nombre_corto('IVA')
                item_1.set_impuesto(item_1_impuesto)

                acutotal = acutotal + grav
                acuiva = acuiva + iva
                miventa = miventa + item.total

                # Agregar el item al DTE
                dte_fel_a_certificar.agregar_item(item_1)

            # Establecer el gran total
            total_fel.set_gran_total(miventa)

            # Establecer el total de impuestos
            totales_impuestos.set_nombre_corto('IVA')
            totales_impuestos.set_total_monto_impuesto(acuiva)
            total_fel.set_total_impuestos(totales_impuestos)

            # Agregar los totales al DTE
            dte_fel_a_certificar.agregar_totales(total_fel)
            
            # agregar adendas al gusto
            fel_adenda = InfileFel.adenda()
            fel_adenda.nombre = "TELEFONO"
            fel_adenda.valor = "7941-4049"
            dte_fel_a_certificar.agregar_adenda(fel_adenda)
            print("#########################################################")

            # Realizar el llamado a la certificada
            certificacion_fel = dte_fel_a_certificar.certificar()
            if certificacion_fel["resultado"]:
                print("UUID:" + certificacion_fel["uuid"])
                print("FECHA:" + certificacion_fel["fecha"])
                print("SERIE:" + certificacion_fel["serie"])
                print("NUMERO:" + str(certificacion_fel["numero"]))

                # Actualizar la venta con la información del DTE
                total = Detalle.objects.filter(token=t).aggregate(tot=Sum('total'))  # Total de la venta
                messages.info(
                    request, f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}"
                )

                Venta.objects.filter(token=t).update(
                    nit=nit, nombre=nombre, direccion=direccion, fecha_fel=str(certificacion_fel['fecha']),
                    estado=1, link=f"https://report.feel.com.gt/ingfacereport/ingfacereport_documento?uuid={certificacion_fel['uuid']}",
                    anula=certificacion_fel['uuid'], serie=certificacion_fel['serie'], numero=certificacion_fel['numero'],
                    total=total['tot']
                )
            else:
                print("No pudo ser certificada")
                print("Descripción: " + certificacion_fel["descripcion"])

                for error_fel in certificacion_fel["descripcion_errores"]:
                    print("Mensaje Error: " + error_fel["fuente"])
                    print("Fuente: " + error_fel["mensaje_error"])
                    print("Categoría: " + error_fel["categoria"])
                    print("Numeral: " + error_fel["numeral"])
                    print("Validación: " + error_fel["validacion"])

        else:
            pass

        ###################################################################################################################################




def anularfel(request, t):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        for datoscliente in Venta.objects.filter(token=t):
            print(datoscliente.nit, datoscliente.nombre, datoscliente.direccion)

        dte_fel_a_anular = InfileFel.fel_dte()

        # realizar el llamado a la certificada
        certificacion_fel = dte_fel_a_anular.anular(str(datoscliente.fecha_fel), '30871360', str(
            datoscliente.fecha_fel), datoscliente.nit, datoscliente.anula, datoscliente.serie)
        if (certificacion_fel["resultado"]):
            print("UUID:" + certificacion_fel["uuid"])
            print("FECHA:" + certificacion_fel["fecha"])
            print("SERIE:" + certificacion_fel["serie"])
            print("numero:" + str(certificacion_fel["numero"]))
        else:
            print("No pudo ser certificada")
            print("Descripcion: " + certificacion_fel["descripcion"])

            for error_fel in certificacion_fel["descripcion_errores"]:
                print("Mensaje Error: " + error_fel["fuente"])
                print("fuente: " + error_fel["mensaje_error"])
                print("categoria: " + error_fel["categoria"])
                print("numeral: " + error_fel["numeral"])
                print("validacion: " + error_fel["validacion"])

        total = Detalle.objects.all().filter(token=t).aggregate(
            tot=Sum('total'))  # total de la venta
        Venta.objects.filter(token=t).update(
            fecha_fel=datoscliente.fecha_fel, estado=2, total=total['tot'])
        anularventa(request, t)
        messages.success(request, 'Anulacion FEL Exitosa')
        return redirect('ListaVentaTodas')


# ========== VISTAS PARA GESTIÓN DE CRÉDITOS FEL ==========

@login_required
def lista_creditos_fel(request):
    """Lista de créditos FEL pendientes (estado=3, tipo=CreditoFel)"""
    # Obtener parámetros de búsqueda y paginación
    search = request.GET.get('search', '')
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 25))

    # Filtro base: créditos FEL pendientes
    creditos = Venta.objects.filter(tipo='CreditoFel', estado=3).order_by('-factura')

    # Aplicar búsqueda si existe
    if search:
        creditos = creditos.filter(
            Q(factura__icontains=search) |
            Q(nombre__icontains=search) |
            Q(nit__icontains=search) |
            Q(fecha__icontains=search)
        )

    # Para AJAX (lazy loading)
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        creditos_page = creditos[offset:offset + limit]
        data = {
            'creditos': [{
                'factura': credito.factura,
                'nombre': credito.nombre,
                'nit': credito.nit,
                'total': str(credito.total),
                'fecha': credito.fecha.strftime('%d/%m/%Y'),
                'usuario': credito.usuario.username,
                'token': str(credito.token),
                'link': credito.link,
                'serie': credito.serie,
                'numero': credito.numero
            } for credito in creditos_page],
            'has_more': creditos.count() > offset + limit
        }
        return JsonResponse(data)

    # Para carga inicial
    creditos_iniciales = creditos[:25]
    context = {
        'creditos': creditos_iniciales,
        'total_creditos': creditos.count()
    }
    return render(request, 'Venta/lista_creditos_fel.html', context)


@login_required
def lista_creditos_fel_pagados(request):
    """Lista de créditos FEL pagados (estado=1, tipo=CreditoFel)"""
    # Obtener parámetros de búsqueda y paginación
    search = request.GET.get('search', '')
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 25))

    # Filtro base: créditos FEL pagados
    creditos = Venta.objects.filter(tipo='CreditoFel', estado=1).order_by('-factura')

    # Aplicar búsqueda si existe
    if search:
        creditos = creditos.filter(
            Q(factura__icontains=search) |
            Q(nombre__icontains=search) |
            Q(nit__icontains=search) |
            Q(fecha__icontains=search)
        )

    # Para AJAX (lazy loading)
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        creditos_page = creditos[offset:offset + limit]
        data = {
            'creditos': [{
                'factura': credito.factura,
                'nombre': credito.nombre,
                'nit': credito.nit,
                'total': str(credito.total),
                'fecha': credito.fecha.strftime('%d/%m/%Y'),
                'usuario': credito.usuario.username,
                'token': str(credito.token),
                'link': credito.link,
                'serie': credito.serie,
                'numero': credito.numero,
                'fecha_pago': credito.pagos_credito_fel.first().fecha_pago.strftime('%d/%m/%Y') if credito.pagos_credito_fel.exists() else ''
            } for credito in creditos_page],
            'has_more': creditos.count() > offset + limit
        }
        return JsonResponse(data)

    # Para carga inicial
    creditos_iniciales = creditos[:25]
    context = {
        'creditos': creditos_iniciales,
        'total_creditos': creditos.count()
    }
    return render(request, 'Venta/lista_creditos_fel_pagados.html', context)


@login_required
def pagar_credito_fel(request, token):
    """Vista para procesar el pago de un crédito FEL"""
    from django.shortcuts import get_object_or_404

    # Obtener la venta de crédito FEL
    venta = get_object_or_404(Venta, token=token, tipo='CreditoFel', estado=3)

    if request.method == 'POST':
        try:
            # Crear registro de pago
            pago = PagoCreditoFel(
                venta=venta,
                monto_pagado=venta.total,
                usuario=request.user,
                observaciones=request.POST.get('observaciones', '')
            )
            pago.save()

            # Cambiar estado de la venta a pagada (estado=1)
            venta.estado = 1
            venta.save()

            messages.success(request, f'Crédito FEL #{venta.factura} pagado exitosamente')
            return redirect('lista_creditos_fel')

        except Exception as e:
            messages.error(request, f'Error al procesar el pago: {str(e)}')

    context = {
        'venta': venta
    }
    return render(request, 'Venta/pagar_credito_fel.html', context)


@login_required
def detalle_credito_fel(request, token):
    """Vista para mostrar detalles de un crédito FEL"""
    from django.shortcuts import get_object_or_404

    venta = get_object_or_404(Venta, token=token, tipo='CreditoFel')
    detalles = Detalle.objects.filter(token=token)
    pagos = PagoCreditoFel.objects.filter(venta=venta)

    context = {
        'venta': venta,
        'detalles': detalles,
        'pagos': pagos
    }
    return render(request, 'Venta/detalle_credito_fel.html', context)