{% extends 'BaseInicio/base.html' %}
{% load static %}
{% block content %}

<div class="full-height-container">
    <div class="card md-2" style="position: relative; overflow: hidden;">
        <img src="{% static 'Base/images/img-3.png' %}" class="card-img" alt="Logo" style="width: 120%; height: auto; object-fit: cover;">
        <div class="card-footer text-center text-sm text-muted" style="bottom: 20px;">
            © <script>
                document.write(new Date().getFullYear())
            </script>, Electrocasa Zacapa
        </div>
    </div>
</div>

<style>
    .full-height-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh; /* Asegura que el contenedor ocupe toda la altura de la pantalla */
        margin: 0;
        padding: 0;
    }

    .card {
        width: 80%; /* Ajusta este valor según el tamaño deseado */
        max-width: 600px; /* Tamaño máximo para evitar que se extienda demasiado en pantallas grandes */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .card-footer {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        background-color: rgba(255, 255, 255, 0.7); /* Fondo semitransparente para el texto del pie de página */
    }
</style>

{% endblock %}
