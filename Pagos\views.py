from django.shortcuts import render,redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from Pagos.models import Gasto, PagoFacturaCredito
from Pagos.reportes import ReporteFacturaCredito
from Producto.models import ProductoFactura
from user.models import User
from django.db.models import Q
import uuid
from django.db.models import Sum
from django.http import HttpResponse
from .forms import GastoForm, PagoFacturaCreditoForm
from decimal import Decimal
from datetime import date, datetime 


@login_required
def gasto(request):
    form = GastoForm()
    if request.method == "POST":
        form = GastoForm(request.POST)
        if form.is_valid():
            try:
                g = Gasto()
                g.nombre = form.cleaned_data['nombre']
                g.cantidad = form.cleaned_data['cantidad']
                g.precio_uni = form.cleaned_data['precio_uni']
                g.total = g.cantidad*g.precio_uni
                g.fecha_pago = request.POST['fecha_pago']
                g.estado = 1
                g.fecha = datetime.today().strftime('%Y-%m-%d')
                g.usuario = request.user.username
                g.save()
                messages.success(request,f'Gasto Ingresado Correctamente!')
                return redirect('TodoGasto')
            except:
                messages.error(request,f'Error al Ingresar Gasto!')
                return redirect('Gasto')


    return render(request,'Pagos/gastos.html',{'form':form})



@login_required
def listagasto(request):
    gastos = Gasto.objects.all()
    return render(request,'Pagos/todogasto.html',{'gastos':gastos})



@login_required
def listagastohoy(request):
    print(datetime.today().strftime('%Y-%m-%d'))
    gastos = Gasto.objects.filter(fecha=datetime.today().strftime('%Y-%m-%d'))
    return render(request,'Pagos/todogastohoy.html',{'gastos':gastos})


@login_required
def lista_pagos_pendientes_factura(request):
    buscar = request.GET.get('buscar', '')

    facturas = ProductoFactura.objects.filter(estado=3)

    if buscar:
        facturas = facturas.filter(
            Q(id_prov__nombre__icontains=buscar) |
            Q(factura__icontains=buscar) |
            Q(serie__icontains=buscar)
        )

    #Corrección aquí
    #facturas = facturas.order_by( '-fecha_factura')
    
    facturas = facturas.order_by('id_prov__nombre', '-fecha_factura')

    facturas_con_abonos = []
    for factura in facturas:
        abonos = PagoFacturaCredito.objects.filter(factura=factura).aggregate(abono_total=Sum('abono'))
        abono_total = abonos['abono_total'] if abonos['abono_total'] is not None else Decimal(0.00)
        queda = factura.total - abono_total
        facturas_con_abonos.append({
            'factura': factura,
            'abono_total': abono_total,
            'queda': queda
        })

    return render(request, 'lista_pagos_pendientes_factura.html', {
        'facturas_con_abonos': facturas_con_abonos,
        'buscar': buscar,
    })






@login_required
def pago_factura_credito(request, factura_id):
    form = PagoFacturaCreditoForm()
    factura = ProductoFactura.objects.get(factura=factura_id)
    abonos_previos = PagoFacturaCredito.objects.filter(factura=factura).aggregate(adeuda=Sum('abono'))

    if abonos_previos['adeuda'] is None:
        abonos_previos['adeuda'] = Decimal(0.00)

    queda = factura.total - abonos_previos['adeuda']

    if request.method == "POST":
        form = PagoFacturaCreditoForm(request.POST)
        if form.is_valid():
            nuevo_abono = form.cleaned_data['abono']
            if queda >= nuevo_abono:
                p = PagoFacturaCredito()
                p.factura = factura
                p.tipo = form.cleaned_data['tipo']
                p.serie = factura.serie  
                p.abono = nuevo_abono
                p.fecha = date.today()  
                p.estado = 1
                p.usuario = request.user
                p.save()

                if queda == nuevo_abono:
                    factura.estado = 1  # Marcar como pagada en su totalidad
                    factura.save()
                    messages.success(request, f'Factura #{factura.factura} pagada en su totalidad')
                    # Redirige al PDF
                    return redirect('generar_pdf_factura_credito', factura_id=factura.factura)
                else:
                    messages.success(request, f'Abono registrado: Q{nuevo_abono}')
                    return redirect('NuevoPagoFacturaCredito', factura_id)
            else:
                messages.error(request, f'El abono de Q{nuevo_abono} supera el saldo pendiente de Q{queda}')
                return redirect('NuevoPagoFacturaCredito', factura_id)

    abonos_realizados = PagoFacturaCredito.objects.filter(factura=factura)

    return render(request, 'pago_factura_credito.html', {
        'form': form,
        'factura': factura,
        'queda': queda,
        'abonos_realizados': abonos_realizados
    })



@login_required
def generar_pdf_factura_credito(request, factura_id):
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="reporte_factura_{factura_id}.pdf"'
    
    reporte = ReporteFacturaCredito(factura_id)
    pdf = reporte.run()
    
    response.write(pdf)
    return response
