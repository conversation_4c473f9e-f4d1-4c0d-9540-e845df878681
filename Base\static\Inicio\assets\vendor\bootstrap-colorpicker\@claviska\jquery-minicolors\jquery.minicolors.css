.minicolors {
  position: relative;
}

.minicolors-sprite {
  background-image: url(jquery.minicolors.png);
}

.minicolors-swatch {
  position: absolute;
  vertical-align: middle;
  background-position: -80px 0;
  border: solid 1px #e6e6f2;
  cursor: text;
  padding: 0;
  margin: 0;
  display: inline-block;
}

.minicolors-swatch-color {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.minicolors input[type=hidden] + .minicolors-swatch {
  width: 28px;
  position: static;
  cursor: pointer;
}

.minicolors input[type=hidden][disabled] + .minicolors-swatch {
  cursor: default;
}

/* Panel */
.minicolors-panel {
  position: absolute;
  width: 173px;
  background: white;
  border: solid 1px #CCC;
  box-shadow: 0 0 20px rgba(0, 0, 0, .2);
  z-index: 99999;
  box-sizing: content-box;
  display: none;
}

.minicolors-panel.minicolors-visible {
  display: block;
}

/* Panel positioning */
.minicolors-position-top .minicolors-panel {
  top: -154px;
}

.minicolors-position-right .minicolors-panel {
  right: 0;
}

.minicolors-position-bottom .minicolors-panel {
  top: auto;
}

.minicolors-position-left .minicolors-panel {
  left: 0;
}

.minicolors-with-opacity .minicolors-panel {
  width: 194px;
}

.minicolors .minicolors-grid {
  position: relative;
  top: 1px;
  left: 1px;
  width: 150px;
  height: 150px;
  background-position: -120px 0;
  cursor: crosshair;
}

.minicolors .minicolors-grid-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 150px;
  height: 150px;
}

.minicolors-slider-saturation .minicolors-grid {
  background-position: -420px 0;
}

.minicolors-slider-saturation .minicolors-grid-inner {
  background-position: -270px 0;
  background-image: inherit;
}

.minicolors-slider-brightness .minicolors-grid {
  background-position: -570px 0;
}

.minicolors-slider-brightness .minicolors-grid-inner {
  background-color: black;
}

.minicolors-slider-wheel .minicolors-grid {
  background-position: -720px 0;
}

.minicolors-slider,
.minicolors-opacity-slider {
  position: absolute;
  top: 1px;
  left: 152px;
  width: 20px;
  height: 150px;
  background-color: white;
  background-position: 0 0;
  cursor: row-resize;
}

.minicolors-slider-saturation .minicolors-slider {
  background-position: -60px 0;
}

.minicolors-slider-brightness .minicolors-slider {
  background-position: -20px 0;
}

.minicolors-slider-wheel .minicolors-slider {
  background-position: -20px 0;
}

.minicolors-opacity-slider {
  left: 173px;
  background-position: -40px 0;
  display: none;
}

.minicolors-with-opacity .minicolors-opacity-slider {
  display: block;
}

/* Pickers */
.minicolors-grid .minicolors-picker {
  position: absolute;
  top: 70px;
  left: 70px;
  width: 12px;
  height: 12px;
  border: solid 1px black;
  border-radius: 10px;
  margin-top: -6px;
  margin-left: -6px;
  background: none;
}

.minicolors-grid .minicolors-picker > div {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  border: solid 2px white;
  box-sizing: content-box;
}

.minicolors-picker {
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 2px;
  background: white;
  border: solid 1px black;
  margin-top: -2px;
  box-sizing: content-box;
}

/* Swatches */
.minicolors-swatches,
.minicolors-swatches li {
  margin: 5px 0 3px 5px;
  padding: 0;
  list-style: none;
  overflow: hidden;
}

.minicolors-swatches .minicolors-swatch {
  position: relative;
  float: left;
  cursor: pointer;
  margin:0 4px 0 0;
}

.minicolors-with-opacity .minicolors-swatches .minicolors-swatch {
  margin-right: 7px;
}

.minicolors-swatch.selected {
  border-color: #000;
}

/* Inline controls */
.minicolors-inline {
  display: inline-block;
}

.minicolors-inline .minicolors-input {
  display: none !important;
}

.minicolors-inline .minicolors-panel {
  position: relative;
  top: auto;
  left: auto;
  box-shadow: none;
  z-index: auto;
  display: inline-block;
}

/* Default theme */
.minicolors-theme-default .minicolors-swatch {
  top: 5px;
  left: 5px;
  width: 18px;
  height: 18px;
}
.minicolors-theme-default .minicolors-swatches .minicolors-swatch {
  margin-bottom: 2px;
  top: 0;
  left: 0;
  width: 18px;
  height: 18px;
}
.minicolors-theme-default.minicolors-position-right .minicolors-swatch {
  left: auto;
  right: 5px;
}
.minicolors-theme-default.minicolors {
  width: auto;
  display: inline-block;
}
.minicolors-theme-default .minicolors-input {
  height: 20px;
  width: auto;
  display: inline-block;
  padding-left: 26px;
}
.minicolors-theme-default.minicolors-position-right .minicolors-input {
  padding-right: 26px;
  padding-left: inherit;
}

/* Bootstrap theme */
.minicolors-theme-bootstrap .minicolors-swatch {
  z-index: 2;
  top: 5px;
  left: 5px;
  width: 28px;
  height: 28px;
  border-radius: 3px;
}
.minicolors-theme-bootstrap .minicolors-swatches .minicolors-swatch {
  margin-bottom: 2px;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
}
.minicolors-theme-bootstrap .minicolors-swatch-color {
  border-radius: inherit;
}
.minicolors-theme-bootstrap.minicolors-position-right > .minicolors-swatch {
  left: auto;
  right: 3px;
}
.minicolors-theme-bootstrap .minicolors-input {
  float: none;
  padding-left: 44px;
  line-height: 1.8;
}
.minicolors-theme-bootstrap.minicolors-position-right .minicolors-input {
  padding-right: 44px;
  padding-left: 12px;
}
.minicolors-theme-bootstrap .minicolors-input.input-lg + .minicolors-swatch {
  top: 4px;
  left: 4px;
  width: 37px;
  height: 37px;
  border-radius: 5px;
}
.minicolors-theme-bootstrap .minicolors-input.input-sm + .minicolors-swatch {
  width: 24px;
  height: 24px;
}
.minicolors-theme-bootstrap .minicolors-input.input-xs + .minicolors-swatch {
  width: 18px;
  height: 18px;
}
.input-group .minicolors-theme-bootstrap:not(:first-child) .minicolors-input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Semantic Ui theme */
.minicolors-theme-semanticui .minicolors-swatch {
  top: 0;
  left: 0;
  padding: 18px;
}
.minicolors-theme-semanticui input {
  text-indent: 30px;
}
