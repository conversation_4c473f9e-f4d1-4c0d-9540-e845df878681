from django import forms
from .models import Gasto


class GastoForm(forms.ModelForm):
   
    class Meta:
        model = Gasto
        fields = ['nombre','cantidad','precio_uni','total']

        widgets = { 
            'nombre': forms.TextInput(attrs={'class': 'form-control','autofocus': True,'require':True}),
            'cantidad': forms.TextInput(attrs={'class': 'form-control','require':True}),
            'precio_uni': forms.TextInput(attrs={'class': 'form-control','require':True}),
            'total': forms.TextInput(attrs={'class': 'form-control','require':True}), 
        }


